/**
 * Simple Hospital Chatbot Widget (No API Required)
 * For testing purposes - uses fallback responses
 */

(function() {
    'use strict';
    
    // Widget configuration
    const config = window.HospitalChatbot || {};
    const hospitalId = config.hospitalId || 'Demo Hospital';
    const theme = config.theme || {};
    
    // Default theme
    const defaultTheme = {
        primaryColor: '#007bff',
        position: 'bottom-right',
        zIndex: 9999,
        borderRadius: '10px'
    };
    
    const finalTheme = { ...defaultTheme, ...theme };
    
    // Widget state
    let isOpen = false;
    
    // Create widget HTML
    function createWidget() {
        const widgetHtml = `
            <div id="hospital-chatbot-container" style="
                position: fixed;
                ${finalTheme.position.includes('bottom') ? 'bottom: 20px;' : 'top: 20px;'}
                ${finalTheme.position.includes('right') ? 'right: 20px;' : 'left: 20px;'}
                z-index: ${finalTheme.zIndex};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <!-- Chat <PERSON> -->
                <div id="chat-button" style="
                    width: 60px;
                    height: 60px;
                    background: ${finalTheme.primaryColor};
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transition: all 0.3s ease;
                " onclick="toggleChat()">
                    <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                    </svg>
                </div>
                
                <!-- Chat Window -->
                <div id="chat-window" style="
                    position: absolute;
                    ${finalTheme.position.includes('bottom') ? 'bottom: 80px;' : 'top: 80px;'}
                    ${finalTheme.position.includes('right') ? 'right: 0;' : 'left: 0;'}
                    width: 350px;
                    height: 500px;
                    background: white;
                    border-radius: ${finalTheme.borderRadius};
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    display: none;
                    flex-direction: column;
                    overflow: hidden;
                ">
                    <!-- Header -->
                    <div style="
                        background: ${finalTheme.primaryColor};
                        color: white;
                        padding: 15px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    ">
                        <div style="display: flex; align-items: center;">
                            <div style="
                                width: 8px;
                                height: 8px;
                                background: #4CAF50;
                                border-radius: 50%;
                                margin-right: 8px;
                            "></div>
                            <span style="font-weight: 600;">${hospitalId} Assistant</span>
                        </div>
                        <div style="cursor: pointer; padding: 5px;" onclick="toggleChat()">
                            <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <div id="chat-messages" style="
                        flex: 1;
                        padding: 15px;
                        overflow-y: auto;
                        background: #f8f9fa;
                    ">
                        <div class="bot-message" style="margin-bottom: 15px;">
                            <div style="
                                background: white;
                                padding: 12px;
                                border-radius: 18px;
                                max-width: 80%;
                                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                            ">
                                Hello! I'm the AI assistant for ${hospitalId}. How can I help you today?
                            </div>
                            <div style="font-size: 11px; color: #666; margin-top: 5px;">Just now</div>
                        </div>
                    </div>
                    
                    <!-- Input -->
                    <div style="
                        padding: 15px;
                        background: white;
                        border-top: 1px solid #eee;
                        display: flex;
                        gap: 10px;
                    ">
                        <input 
                            type="text" 
                            id="chat-input" 
                            placeholder="Type your message..."
                            style="
                                flex: 1;
                                border: 1px solid #ddd;
                                border-radius: 20px;
                                padding: 10px 15px;
                                outline: none;
                                font-size: 14px;
                            "
                            onkeypress="handleKeyPress(event)"
                        >
                        <button 
                            onclick="sendMessage()"
                            style="
                                background: ${finalTheme.primaryColor};
                                border: none;
                                border-radius: 50%;
                                width: 40px;
                                height: 40px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                            "
                        >
                            <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', widgetHtml);
    }
    
    // Toggle chat window
    window.toggleChat = function() {
        const chatWindow = document.getElementById('chat-window');
        const chatButton = document.getElementById('chat-button');
        
        isOpen = !isOpen;
        
        if (isOpen) {
            chatWindow.style.display = 'flex';
            chatButton.style.transform = 'scale(0.9)';
            document.getElementById('chat-input').focus();
        } else {
            chatWindow.style.display = 'none';
            chatButton.style.transform = 'scale(1)';
        }
    };
    
    // Handle enter key press
    window.handleKeyPress = function(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    };
    
    // Send message
    window.sendMessage = function() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // Add user message
        addMessage(message, 'user');
        input.value = '';
        
        // Show typing indicator
        showTypingIndicator();
        
        // Simulate response after delay
        setTimeout(() => {
            hideTypingIndicator();
            const response = getSimulatedResponse(message);
            addMessage(response, 'bot');
        }, 1500);
    };
    
    // Get simulated response
    function getSimulatedResponse(message) {
        const msg = message.toLowerCase();
        
        // Emergency responses
        if (msg.includes('chest pain') || msg.includes('heart attack') || msg.includes('emergency')) {
            return "🚨 This sounds like a medical emergency. Please call 911 immediately or visit our Emergency Department on the east side of the building. Do not delay seeking immediate medical attention.";
        }
        
        // Visiting hours
        if (msg.includes('visiting hours') || msg.includes('visit') || msg.includes('hours')) {
            return "Our visiting hours are:\n• General Wards: 9:00 AM - 8:00 PM daily\n• ICU: 2:00 PM - 4:00 PM and 6:00 PM - 8:00 PM\n• Emergency Department: Family allowed 24/7 (one person at a time)";
        }
        
        // Appointments
        if (msg.includes('appointment') || msg.includes('book') || msg.includes('schedule')) {
            return "I can help you book an appointment! Please call (************* or visit our online portal at www.citygeneralhospital.com. What department would you like to visit?";
        }
        
        // Departments
        if (msg.includes('department') || msg.includes('service')) {
            return "We offer these departments:\n• Emergency Department (24/7)\n• Cardiology\n• Pediatrics\n• Orthopedics\n• General Medicine\n• Radiology\n• Laboratory";
        }
        
        // Insurance
        if (msg.includes('insurance') || msg.includes('billing') || msg.includes('cost')) {
            return "We accept most major insurance plans including Blue Cross Blue Shield, Aetna, Cigna, Medicare, and Medicaid. Please contact our billing department at (************* for specific coverage questions.";
        }
        
        // Doctors
        if (msg.includes('doctor') || msg.includes('physician')) {
            return "Our experienced doctors include:\n• Dr. Sarah Johnson - Chief of Cardiology\n• Dr. Michael Chen - Emergency Medicine Director\n• Dr. Lisa Rodriguez - Pediatrics Department Head\n• Dr. James Wilson - Orthopedic Surgery";
        }
        
        // Parking
        if (msg.includes('parking') || msg.includes('park')) {
            return "Parking information:\n• Main parking garage: $5/day\n• Valet parking: $10/day\n• Free parking for emergency patients (first 2 hours)\n• Handicap accessible parking available";
        }
        
        // Greetings
        if (msg.includes('hello') || msg.includes('hi') || msg.includes('hey')) {
            return "Hello! Welcome to City General Hospital. I'm here to help you with information about our services, appointments, visiting hours, and more. How can I assist you today?";
        }
        
        // Default response
        return "Thank you for your message! I'm here to help with information about City General Hospital. You can ask me about:\n• Visiting hours\n• Appointments\n• Departments and services\n• Insurance and billing\n• Parking information\n\nFor immediate assistance, please call (*************.";
    }
    
    // Add message to chat
    function addMessage(message, sender) {
        const messagesContainer = document.getElementById('chat-messages');
        const isBot = sender === 'bot';
        
        const messageHtml = `
            <div class="${sender}-message" style="
                margin-bottom: 15px;
                display: flex;
                ${isBot ? 'justify-content: flex-start' : 'justify-content: flex-end'};
            ">
                <div>
                    <div style="
                        background: ${isBot ? 'white' : finalTheme.primaryColor};
                        color: ${isBot ? '#333' : 'white'};
                        padding: 12px;
                        border-radius: 18px;
                        max-width: 80%;
                        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        word-wrap: break-word;
                        white-space: pre-line;
                    ">
                        ${message}
                    </div>
                    <div style="
                        font-size: 11px; 
                        color: #666; 
                        margin-top: 5px;
                        text-align: ${isBot ? 'left' : 'right'};
                    ">
                        Just now
                    </div>
                </div>
            </div>
        `;
        
        messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Show typing indicator
    function showTypingIndicator() {
        const messagesContainer = document.getElementById('chat-messages');
        
        const typingHtml = `
            <div id="typing-indicator" style="margin-bottom: 15px;">
                <div style="
                    background: white;
                    padding: 12px;
                    border-radius: 18px;
                    max-width: 80%;
                    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                    display: flex;
                    align-items: center;
                    gap: 4px;
                ">
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #ccc;
                        border-radius: 50%;
                        animation: typing 1.4s infinite ease-in-out;
                    "></div>
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #ccc;
                        border-radius: 50%;
                        animation: typing 1.4s infinite ease-in-out 0.2s;
                    "></div>
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #ccc;
                        border-radius: 50%;
                        animation: typing 1.4s infinite ease-in-out 0.4s;
                    "></div>
                </div>
            </div>
        `;
        
        messagesContainer.insertAdjacentHTML('beforeend', typingHtml);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Add CSS animation
        if (!document.getElementById('typing-animation')) {
            const style = document.createElement('style');
            style.id = 'typing-animation';
            style.textContent = `
                @keyframes typing {
                    0%, 60%, 100% { transform: translateY(0); opacity: 0.5; }
                    30% { transform: translateY(-10px); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // Hide typing indicator
    function hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    // Initialize widget
    function init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createWidget);
        } else {
            createWidget();
        }
    }
    
    // Start initialization
    init();
    
})();
