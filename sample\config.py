"""
Configuration for Hospital Chatbot SaaS Database Setup
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Database configuration
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "hospital_chatbot_saas",
    "username": "hospital_chatbot_user",
    "password": "HospitalBot2024!",
    "superuser": "postgres",  # PostgreSQL superuser for database creation
    "superuser_password": None  # Will be prompted if needed
}

# Data generation settings
DATA_CONFIG = {
    "hospitals": 10,
    "users_per_hospital": 5,  # 1 admin + 4 regular users
    "patients_per_hospital": 50,
    "appointments_per_hospital": 100,
    "chat_sessions_per_hospital": 200,
    "messages_per_session": 5,  # Average messages per session
    "days_of_history": 90  # Generate data for last 90 days
}

# Hospital types and configurations
HOSPITAL_TYPES = [
    {
        "name": "City General Hospital",
        "type": "General",
        "domain": "citygeneral.com",
        "subscription_plan": "premium",
        "departments": ["Emergency", "Cardiology", "Pediatrics", "Surgery", "Internal Medicine"]
    },
    {
        "name": "Children's Medical Center",
        "type": "Pediatric",
        "domain": "childrensmedical.org",
        "subscription_plan": "enterprise",
        "departments": ["Pediatrics", "Pediatric Surgery", "NICU", "Child Psychology"]
    },
    {
        "name": "Heart & Vascular Institute",
        "type": "Specialty",
        "domain": "heartvascular.com",
        "subscription_plan": "basic",
        "departments": ["Cardiology", "Cardiac Surgery", "Vascular Surgery"]
    },
    {
        "name": "Metro Emergency Hospital",
        "type": "Emergency",
        "domain": "metroemergency.org",
        "subscription_plan": "premium",
        "departments": ["Emergency", "Trauma", "Critical Care"]
    },
    {
        "name": "Women's Health Clinic",
        "type": "Specialty",
        "domain": "womenshealth.com",
        "subscription_plan": "trial",
        "departments": ["Obstetrics", "Gynecology", "Maternal-Fetal Medicine"]
    },
    {
        "name": "Orthopedic Specialty Center",
        "type": "Specialty",
        "domain": "orthocenter.com",
        "subscription_plan": "basic",
        "departments": ["Orthopedics", "Sports Medicine", "Physical Therapy"]
    },
    {
        "name": "Cancer Treatment Center",
        "type": "Specialty",
        "domain": "cancercenter.org",
        "subscription_plan": "enterprise",
        "departments": ["Oncology", "Radiation Therapy", "Hematology"]
    },
    {
        "name": "Mental Health Institute",
        "type": "Psychiatric",
        "domain": "mentalhealth.org",
        "subscription_plan": "premium",
        "departments": ["Psychiatry", "Psychology", "Addiction Treatment"]
    },
    {
        "name": "Rehabilitation Hospital",
        "type": "Rehabilitation",
        "domain": "rehabhosp.com",
        "subscription_plan": "basic",
        "departments": ["Physical Therapy", "Occupational Therapy", "Speech Therapy"]
    },
    {
        "name": "Community Health Center",
        "type": "Community",
        "domain": "communityhealth.org",
        "subscription_plan": "trial",
        "departments": ["Family Medicine", "Urgent Care", "Preventive Care"]
    }
]

# Subscription plan configurations
SUBSCRIPTION_PLANS = {
    "trial": {
        "name": "Trial",
        "price": 0,
        "message_limit": 1000,
        "duration_days": 14
    },
    "basic": {
        "name": "Basic",
        "price": 49,
        "message_limit": 5000,
        "duration_days": 30
    },
    "premium": {
        "name": "Premium",
        "price": 149,
        "message_limit": 20000,
        "duration_days": 30
    },
    "enterprise": {
        "name": "Enterprise",
        "price": 499,
        "message_limit": 100000,
        "duration_days": 30
    }
}

# Chat intents and sample messages
CHAT_INTENTS = {
    "greet": [
        "Hello", "Hi", "Good morning", "Good afternoon", "Hey there"
    ],
    "book_appointment": [
        "I want to book an appointment",
        "Schedule an appointment",
        "I need to see a doctor",
        "Can I make an appointment?"
    ],
    "check_symptoms": [
        "I have a headache",
        "I'm feeling sick",
        "I have chest pain",
        "My stomach hurts",
        "I have a fever"
    ],
    "ask_department_info": [
        "What departments do you have?",
        "Tell me about cardiology",
        "Emergency department hours",
        "Pediatrics information"
    ],
    "ask_visiting_hours": [
        "What are your visiting hours?",
        "When can I visit?",
        "Hospital hours",
        "When are you open?"
    ],
    "insurance_query": [
        "Do you accept my insurance?",
        "Insurance coverage",
        "What insurance do you take?",
        "Billing information"
    ],
    "emergency_contact": [
        "Emergency number",
        "I need help urgently",
        "Emergency services",
        "Urgent care"
    ]
}

def get_database_url(include_db=True):
    """Get database URL for SQLAlchemy"""
    config = DATABASE_CONFIG
    if include_db:
        return f"postgresql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
    else:
        return f"postgresql://{config['username']}:{config['password']}@{config['host']}:{config['port']}/postgres"

def get_superuser_connection():
    """Get connection as PostgreSQL superuser"""
    config = DATABASE_CONFIG
    password = config.get('superuser_password')
    
    if not password:
        import getpass
        password = getpass.getpass(f"Enter password for PostgreSQL superuser '{config['superuser']}': ")
    
    return psycopg2.connect(
        host=config['host'],
        port=config['port'],
        user=config['superuser'],
        password=password,
        database='postgres'
    )

def get_db_connection():
    """Get database connection for application user"""
    try:
        config = DATABASE_CONFIG
        return psycopg2.connect(
            host=config['host'],
            port=config['port'],
            user=config['username'],
            password=config['password'],
            database=config['database']
        )
    except Exception:
        return None

def get_sqlalchemy_engine():
    """Get SQLAlchemy engine"""
    return create_engine(get_database_url())

def get_session():
    """Get SQLAlchemy session"""
    engine = get_sqlalchemy_engine()
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()
