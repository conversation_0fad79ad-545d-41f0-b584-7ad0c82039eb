import React, { useState, useEffect } from 'react';

const Integration = () => {
  const [hmsType, setHmsType] = useState('');
  const [credentials, setCredentials] = useState({});
  const [testConnection, setTestConnection] = useState(null);
  
  const hmsOptions = [
    { value: 'epic', label: 'Epic' },
    { value: 'cerner', label: 'Cerner' },
    { value: 'allscripts', label: 'Allscripts' },
    { value: 'custom', label: 'Custom Webhook' }
  ];
  
  const handleTestConnection = async () => {
    try {
      const response = await fetch('/api/integration/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ hms_type: hmsType, credentials })
      });
      
      const result = await response.json();
      setTestConnection(result.success ? 'success' : 'error');
    } catch (error) {
      setTestConnection('error');
    }
  };
  
  return (
    <div className="integration-settings">
      <h2>HMS Integration Settings</h2>
      
      <div className="form-group">
        <label>Hospital Management System:</label>
        <select value={hmsType} onChange={(e) => setHmsType(e.target.value)}>
          <option value="">Select HMS</option>
          {hmsOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
      
      {/* Dynamic credential forms based on HMS type */}
      {hmsType === 'epic' && (
        <div>
          <input
            type="text"
            placeholder="FHIR Base URL"
            onChange={(e) => setCredentials({...credentials, base_url: e.target.value})}
          />
          <input
            type="text"
            placeholder="Client ID"
            onChange={(e) => setCredentials({...credentials, client_id: e.target.value})}
          />
          <input
            type="password"
            placeholder="Client Secret"
            onChange={(e) => setCredentials({...credentials, client_secret: e.target.value})}
          />
        </div>
      )}
      
      <button onClick={handleTestConnection}>Test Connection</button>
      
      {testConnection && (
        <div className={`test-result ${testConnection}`}>
          {testConnection === 'success' ? '✅ Connection successful!' : '❌ Connection failed'}
        </div>
      )}
    </div>
  );
};