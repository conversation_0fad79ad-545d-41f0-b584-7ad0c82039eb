#!/usr/bin/env python3
"""
Debug Environment Variables
Check if .env file is being loaded correctly
"""

import os
import sys
from pathlib import Path

def debug_environment():
    """Debug environment variable loading"""
    print("🔍 Debugging Environment Variables")
    print("=" * 50)
    
    # Check current working directory
    print(f"Current working directory: {os.getcwd()}")
    
    # Check if .env file exists
    env_files = [
        ".env",
        "backend/.env",
        Path(__file__).parent / ".env",
        Path(__file__).parent.parent / ".env"
    ]
    
    print("\n📁 Checking for .env files:")
    for env_file in env_files:
        env_path = Path(env_file)
        if env_path.exists():
            print(f"✅ Found: {env_path.absolute()}")
            
            # Read and check content
            with open(env_path, 'r') as f:
                content = f.read()
                if "GOOGLE_API_KEY" in content:
                    lines = content.split('\n')
                    for line in lines:
                        if line.startswith('GOOGLE_API_KEY'):
                            key_value = line.split('=', 1)[1] if '=' in line else ''
                            key_value = key_value.strip().strip('"').strip("'")
                            if key_value and key_value != "your-gemini-api-key-here":
                                print(f"   📋 GOOGLE_API_KEY found: {key_value[:10]}...{key_value[-5:]}")
                            else:
                                print(f"   ⚠️  GOOGLE_API_KEY is placeholder or empty")
                else:
                    print(f"   ❌ GOOGLE_API_KEY not found in file")
        else:
            print(f"❌ Not found: {env_path}")
    
    # Test environment variable loading methods
    print("\n🧪 Testing environment variable loading:")
    
    # Method 1: Direct os.getenv
    print("1. Direct os.getenv():")
    direct_key = os.getenv('GOOGLE_API_KEY')
    if direct_key:
        print(f"   ✅ Found: {direct_key[:10]}...{direct_key[-5:]}")
    else:
        print("   ❌ Not found")
    
    # Method 2: Load with python-dotenv
    try:
        from dotenv import load_dotenv
        print("2. Using python-dotenv:")
        
        # Try loading from current directory
        load_dotenv()
        dotenv_key = os.getenv('GOOGLE_API_KEY')
        if dotenv_key:
            print(f"   ✅ Found after load_dotenv(): {dotenv_key[:10]}...{dotenv_key[-5:]}")
        else:
            print("   ❌ Not found after load_dotenv()")
        
        # Try loading from specific path
        env_path = Path(__file__).parent / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            specific_key = os.getenv('GOOGLE_API_KEY')
            if specific_key:
                print(f"   ✅ Found after load_dotenv(specific_path): {specific_key[:10]}...{specific_key[-5:]}")
            else:
                print("   ❌ Not found after load_dotenv(specific_path)")
        
    except ImportError:
        print("2. python-dotenv not installed")
    
    # Method 3: Manual parsing
    print("3. Manual .env parsing:")
    env_path = Path(__file__).parent / ".env"
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                if line.startswith('GOOGLE_API_KEY'):
                    key_value = line.split('=', 1)[1] if '=' in line else ''
                    key_value = key_value.strip().strip('"').strip("'")
                    if key_value:
                        print(f"   ✅ Manually parsed: {key_value[:10]}...{key_value[-5:]}")
                        # Set it manually
                        os.environ['GOOGLE_API_KEY'] = key_value
                        print(f"   ✅ Set in os.environ")
                    break
    
    # Final check
    print("\n🎯 Final environment check:")
    final_key = os.getenv('GOOGLE_API_KEY')
    if final_key and final_key != "your-gemini-api-key-here":
        print(f"✅ GOOGLE_API_KEY is available: {final_key[:10]}...{final_key[-5:]}")
        return True
    else:
        print("❌ GOOGLE_API_KEY is not properly set")
        return False

def test_gemini_with_env():
    """Test Gemini with environment variable"""
    print("\n🤖 Testing Gemini with Environment Variable")
    print("=" * 50)
    
    try:
        # Load environment
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key or api_key == "your-gemini-api-key-here":
            print("❌ API key not configured")
            return False
        
        # Test Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Say 'Hello from environment variable!'")
        
        print("✅ Gemini test successful!")
        print(f"📝 Response: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        return False

def main():
    """Main debug function"""
    print("🏥 Hospital Chatbot SaaS - Environment Debug")
    print("=" * 60)
    
    # Debug environment loading
    env_ok = debug_environment()
    
    if env_ok:
        # Test Gemini
        gemini_ok = test_gemini_with_env()
        
        if gemini_ok:
            print("\n🎉 Everything is working! Your environment is properly configured.")
        else:
            print("\n⚠️  Environment is configured but Gemini test failed.")
    else:
        print("\n❌ Environment configuration issues found.")
        print("\n💡 Solutions:")
        print("1. Make sure .env file exists in backend directory")
        print("2. Check GOOGLE_API_KEY value in .env file")
        print("3. Get API key from: https://makersuite.google.com/app/apikey")
        print("4. Make sure no extra spaces or quotes around the key")

if __name__ == "__main__":
    main()
