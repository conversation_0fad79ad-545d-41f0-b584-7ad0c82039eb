#!/usr/bin/env python3
"""
Verify Database Data Script
Checks if data was inserted correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from config import get_database_url

def main():
    """Verify database data"""
    print("🔍 Verifying Database Data")
    print("=" * 50)
    
    try:
        engine = create_engine(get_database_url())
        
        with engine.connect() as conn:
            # Check table counts
            tables_to_check = [
                'hospitals', 'users', 'patients', 'appointments',
                'chat_sessions', 'chat_messages', 'usage_metrics',
                'subscription_history', 'system_configurations'
            ]
            
            print("📊 Table Record Counts:")
            total_records = 0
            
            for table in tables_to_check:
                try:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.fetchone()[0]
                    print(f"   {table:20}: {count:6,} records")
                    total_records += count
                except Exception as e:
                    print(f"   {table:20}: ❌ Error - {e}")
            
            print(f"\n📈 Total Records: {total_records:,}")
            
            # Check sample data
            print("\n🏥 Sample Hospitals:")
            result = conn.execute(text("""
                SELECT name, subscription_plan, monthly_message_count, monthly_message_limit 
                FROM hospitals 
                ORDER BY name 
                LIMIT 5
            """))
            
            for row in result:
                usage_pct = (row[2] / row[3] * 100) if row[3] > 0 else 0
                print(f"   {row[0]:25} | {row[1]:10} | Usage: {usage_pct:5.1f}%")
            
            # Check recent chat activity
            print("\n💬 Recent Chat Activity:")
            result = conn.execute(text("""
                SELECT h.name, COUNT(cs.id) as sessions, COUNT(cm.id) as messages
                FROM hospitals h
                LEFT JOIN chat_sessions cs ON h.id = cs.hospital_id
                LEFT JOIN chat_messages cm ON cs.id = cm.session_id
                GROUP BY h.id, h.name
                ORDER BY messages DESC
                LIMIT 5
            """))
            
            for row in result:
                print(f"   {row[0]:25} | Sessions: {row[1]:4} | Messages: {row[2]:5}")
            
            print("\n✅ Database verification completed successfully!")
            
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
