"""
Simple Gemini Service for Hospital Chatbot
Minimal, robust implementation without complex safety settings
"""

import os
from dotenv import load_dotenv
from typing import Dict, Any, Optional

# Load environment variables
load_dotenv()

class SimpleGeminiService:
    def __init__(self):
        """Initialize simple Gemini service"""
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp")
        self.model = None
        
        if not self.api_key or self.api_key == "your-gemini-api-key-here":
            print("⚠️  Gemini API key not configured")
            return
        
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.api_key)
            
            # Try different models in order of preference
            models_to_try = [
                "gemini-2.0-flash-exp",
                "gemini-pro",
                "gemini-1.5-pro"
            ]
            
            for model_name in models_to_try:
                try:
                    self.model = genai.GenerativeModel(model_name)
                    self.model_name = model_name
                    print(f"✅ Gemini model initialized: {model_name}")
                    break
                except Exception as e:
                    print(f"⚠️  Failed to load {model_name}: {e}")
                    continue
            
            if not self.model:
                print("❌ Failed to initialize any Gemini model")
                
        except ImportError:
            print("❌ google-generativeai package not installed")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini: {e}")
    
    def is_available(self) -> bool:
        """Check if Gemini service is available"""
        return self.model is not None
    
    def generate_response(self, message: str, hospital_info: Optional[Dict] = None) -> str:
        """Generate response using Gemini"""
        if not self.is_available():
            return self._fallback_response(message)
        
        try:
            # Create hospital-specific prompt
            prompt = self._create_prompt(message, hospital_info)
            
            # Generate response
            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            print(f"⚠️  Gemini generation failed: {e}")
            return self._fallback_response(message)
    
    def _create_prompt(self, message: str, hospital_info: Optional[Dict] = None) -> str:
        """Create hospital-specific prompt"""
        
        hospital_context = ""
        if hospital_info:
            hospital_context = f"""
Hospital Information:
- Name: {hospital_info.get('name', 'Our Hospital')}
- Departments: {', '.join(hospital_info.get('departments', ['General Medicine']))}
- Emergency Contact: {hospital_info.get('emergency_contact', '911')}
- Phone: {hospital_info.get('phone', 'Contact us for phone number')}
"""
        
        prompt = f"""
You are a helpful AI assistant for a hospital chatbot. You are professional, empathetic, and helpful.

{hospital_context}

Guidelines:
1. Be empathetic and professional
2. Help with appointment booking, general information, and directions
3. For medical emergencies, direct to call 911 or visit Emergency Department
4. Do NOT provide medical diagnosis or treatment advice
5. For specific medical questions, suggest consulting with healthcare professionals
6. Keep responses concise but helpful

Patient message: "{message}"

Provide a helpful response:
"""
        return prompt
    
    def _fallback_response(self, message: str) -> str:
        """Provide fallback response when Gemini is not available"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            return "Hello! Welcome to our hospital. How can I help you today?"
        
        elif any(word in message_lower for word in ["appointment", "book", "schedule"]):
            return "I can help you book an appointment. What department would you like to visit? You can also call us directly to schedule."
        
        elif any(word in message_lower for word in ["emergency", "urgent", "pain", "help"]):
            return "For medical emergencies, please call 911 or visit our Emergency Department immediately. For urgent but non-emergency care, please contact us directly."
        
        elif any(word in message_lower for word in ["hours", "time", "open"]):
            return "Our visiting hours are typically 9 AM to 8 PM daily. Please contact us for current hours and any department-specific schedules."
        
        elif any(word in message_lower for word in ["department", "doctor", "specialist"]):
            return "We have several departments including Emergency, Cardiology, Pediatrics, Orthopedics, and General Medicine. What type of care are you looking for?"
        
        elif any(word in message_lower for word in ["insurance", "billing", "cost"]):
            return "For insurance and billing questions, please contact our billing department. We accept most major insurance plans and offer payment assistance programs."
        
        else:
            return "Thank you for contacting us. Our team will assist you shortly. For immediate assistance, please call our main number or visit our website."

# Global instance
simple_gemini_service = SimpleGeminiService()
