from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from pydantic import BaseModel
from typing import List, Dict, Any
from datetime import datetime, timedelta, date

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database import get_db
from database.models import Hospital, ChatSession, ChatMessage, Appointment, Patient
from auth import get_current_hospital

router = APIRouter()

# Pydantic models
class DashboardStats(BaseModel):
    total_conversations: int
    total_messages: int
    total_appointments: int
    total_patients: int
    active_sessions: int
    avg_session_duration: float
    top_intents: List[Dict[str, Any]]
    recent_activity: List[Dict[str, Any]]

class ConversationMetrics(BaseModel):
    date: str
    conversations: int
    messages: int
    avg_confidence: float

class IntentAnalytics(BaseModel):
    intent: str
    count: int
    percentage: float
    avg_confidence: float

class AppointmentMetrics(BaseModel):
    date: str
    total: int
    confirmed: int
    pending: int
    cancelled: int

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    days: int = Query(30, description="Number of days to analyze"),
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get comprehensive dashboard statistics"""

    # Date range for analysis
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Total conversations
    total_conversations = db.query(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        ChatSession.created_at >= start_date
    ).count()

    # Total messages
    total_messages = db.query(ChatMessage).join(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        ChatMessage.created_at >= start_date
    ).count()

    # Total appointments
    total_appointments = db.query(Appointment).filter(
        Appointment.hospital_id == hospital.id,
        Appointment.created_at >= start_date
    ).count()

    # Total patients
    total_patients = db.query(Patient).filter(
        Patient.hospital_id == hospital.id,
        Patient.created_at >= start_date
    ).count()

    # Active sessions (not ended)
    active_sessions = db.query(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        ChatSession.ended_at.is_(None)
    ).count()

    # Average session duration
    completed_sessions = db.query(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        ChatSession.ended_at.isnot(None),
        ChatSession.created_at >= start_date
    ).all()

    if completed_sessions:
        durations = [
            (session.ended_at - session.created_at).total_seconds() / 60
            for session in completed_sessions
        ]
        avg_session_duration = sum(durations) / len(durations)
    else:
        avg_session_duration = 0.0

    # Top intents
    intent_counts = db.query(
        ChatMessage.intent,
        func.count(ChatMessage.intent).label('count'),
        func.avg(ChatMessage.confidence).label('avg_confidence')
    ).join(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        ChatMessage.intent.isnot(None),
        ChatMessage.created_at >= start_date
    ).group_by(ChatMessage.intent).order_by(func.count(ChatMessage.intent).desc()).limit(5).all()

    top_intents = [
        {
            "intent": intent,
            "count": count,
            "avg_confidence": round(avg_confidence or 0, 2)
        }
        for intent, count, avg_confidence in intent_counts
    ]

    # Recent activity
    recent_messages = db.query(ChatMessage).join(ChatSession).filter(
        ChatSession.hospital_id == hospital.id
    ).order_by(ChatMessage.created_at.desc()).limit(10).all()

    recent_activity = [
        {
            "type": "message",
            "content": msg.message[:100] + "..." if len(msg.message) > 100 else msg.message,
            "sender": msg.sender,
            "timestamp": msg.created_at.isoformat()
        }
        for msg in recent_messages
    ]

    return DashboardStats(
        total_conversations=total_conversations,
        total_messages=total_messages,
        total_appointments=total_appointments,
        total_patients=total_patients,
        active_sessions=active_sessions,
        avg_session_duration=round(avg_session_duration, 2),
        top_intents=top_intents,
        recent_activity=recent_activity
    )

@router.get("/conversation-metrics", response_model=List[ConversationMetrics])
async def get_conversation_metrics(
    days: int = Query(30, description="Number of days to analyze"),
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get daily conversation metrics"""

    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)

    # Generate date range
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)

    metrics = []
    for date_obj in date_range:
        # Conversations for this date
        conversations = db.query(ChatSession).filter(
            ChatSession.hospital_id == hospital.id,
            func.date(ChatSession.created_at) == date_obj
        ).count()

        # Messages for this date
        messages = db.query(ChatMessage).join(ChatSession).filter(
            ChatSession.hospital_id == hospital.id,
            func.date(ChatMessage.created_at) == date_obj
        ).count()

        # Average confidence for this date
        avg_confidence_result = db.query(
            func.avg(ChatMessage.confidence)
        ).join(ChatSession).filter(
            ChatSession.hospital_id == hospital.id,
            func.date(ChatMessage.created_at) == date_obj,
            ChatMessage.confidence.isnot(None)
        ).scalar()

        avg_confidence = round(avg_confidence_result or 0, 2)

        metrics.append(ConversationMetrics(
            date=date_obj.isoformat(),
            conversations=conversations,
            messages=messages,
            avg_confidence=avg_confidence
        ))

    return metrics

@router.get("/intent-analytics", response_model=List[IntentAnalytics])
async def get_intent_analytics(
    days: int = Query(30, description="Number of days to analyze"),
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get intent analytics"""

    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Get intent counts and confidence
    intent_data = db.query(
        ChatMessage.intent,
        func.count(ChatMessage.intent).label('count'),
        func.avg(ChatMessage.confidence).label('avg_confidence')
    ).join(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        ChatMessage.intent.isnot(None),
        ChatMessage.created_at >= start_date
    ).group_by(ChatMessage.intent).all()

    total_intents = sum(count for _, count, _ in intent_data)

    analytics = []
    for intent, count, avg_confidence in intent_data:
        percentage = round((count / total_intents) * 100, 2) if total_intents > 0 else 0

        analytics.append(IntentAnalytics(
            intent=intent,
            count=count,
            percentage=percentage,
            avg_confidence=round(avg_confidence or 0, 2)
        ))

    # Sort by count descending
    analytics.sort(key=lambda x: x.count, reverse=True)

    return analytics

@router.get("/appointment-metrics", response_model=List[AppointmentMetrics])
async def get_appointment_metrics(
    days: int = Query(30, description="Number of days to analyze"),
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get daily appointment metrics"""

    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=days)

    # Generate date range
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)

    metrics = []
    for date_obj in date_range:
        # Total appointments for this date
        total = db.query(Appointment).filter(
            Appointment.hospital_id == hospital.id,
            func.date(Appointment.created_at) == date_obj
        ).count()

        # Confirmed appointments
        confirmed = db.query(Appointment).filter(
            Appointment.hospital_id == hospital.id,
            func.date(Appointment.created_at) == date_obj,
            Appointment.status == 'confirmed'
        ).count()

        # Pending appointments
        pending = db.query(Appointment).filter(
            Appointment.hospital_id == hospital.id,
            func.date(Appointment.created_at) == date_obj,
            Appointment.status == 'pending'
        ).count()

        # Cancelled appointments
        cancelled = db.query(Appointment).filter(
            Appointment.hospital_id == hospital.id,
            func.date(Appointment.created_at) == date_obj,
            Appointment.status == 'cancelled'
        ).count()

        metrics.append(AppointmentMetrics(
            date=date_obj.isoformat(),
            total=total,
            confirmed=confirmed,
            pending=pending,
            cancelled=cancelled
        ))

    return metrics

@router.get("/export/conversations")
async def export_conversations(
    start_date: date = Query(..., description="Start date for export"),
    end_date: date = Query(..., description="End date for export"),
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Export conversation data for analysis"""

    # Get sessions in date range
    sessions = db.query(ChatSession).filter(
        ChatSession.hospital_id == hospital.id,
        func.date(ChatSession.created_at) >= start_date,
        func.date(ChatSession.created_at) <= end_date
    ).all()

    export_data = []
    for session in sessions:
        messages = db.query(ChatMessage).filter(
            ChatMessage.session_id == session.id
        ).order_by(ChatMessage.created_at.asc()).all()

        session_data = {
            "session_id": str(session.id),
            "visitor_id": session.visitor_id,
            "created_at": session.created_at.isoformat(),
            "ended_at": session.ended_at.isoformat() if session.ended_at else None,
            "messages": [
                {
                    "message": msg.message,
                    "sender": msg.sender,
                    "intent": msg.intent,
                    "confidence": msg.confidence,
                    "timestamp": msg.created_at.isoformat()
                }
                for msg in messages
            ]
        }
        export_data.append(session_data)

    return {
        "hospital_id": str(hospital.id),
        "export_date": datetime.utcnow().isoformat(),
        "date_range": {
            "start": start_date.isoformat(),
            "end": end_date.isoformat()
        },
        "total_sessions": len(export_data),
        "data": export_data
    }
