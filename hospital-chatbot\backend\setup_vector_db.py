#!/usr/bin/env python3
"""
Setup Vector Database for Hospital Chatbot SaaS
Initialize Qdrant with hospital-specific knowledge
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables
load_dotenv()

def setup_vector_database():
    """Setup and populate vector database"""
    print("🗄️  Setting up Vector Database for Hospital Chatbot SaaS")
    print("=" * 60)
    
    try:
        from services.qdrant_service import qdrant_service
        from services.enhanced_gemini_service import enhanced_gemini_service
        
        if not qdrant_service.is_available():
            print("❌ Qdrant service not available")
            print("💡 Make sure Qdrant is running: docker-compose up qdrant")
            return False
        
        print("✅ Qdrant service is available")
        
        # Get hospital data from PostgreSQL
        hospitals = get_sample_hospitals()
        
        if not hospitals:
            print("⚠️  No hospitals found in database")
            print("💡 Run sample data setup first: cd sample && python setup_complete.py")
            return False
        
        # Initialize knowledge for each hospital
        docs_directory = current_dir.parent / "docs"
        
        for hospital in hospitals:
            hospital_id = hospital['name']
            print(f"\n🏥 Initializing knowledge for: {hospital_id}")
            
            # Load hospital documents into vector database
            success = enhanced_gemini_service.initialize_hospital_knowledge(
                hospital_id=hospital_id,
                docs_directory=str(docs_directory)
            )
            
            if success:
                # Get stats
                stats = enhanced_gemini_service.get_hospital_knowledge_stats(hospital_id)
                print(f"   ✅ Loaded {stats.get('document_count', 0)} documents")
            else:
                print(f"   ⚠️  No documents found for {hospital_id}")
        
        print("\n🎉 Vector database setup completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Install required packages: pip install qdrant-client sentence-transformers")
        return False
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False

def get_sample_hospitals():
    """Get hospital data from PostgreSQL"""
    try:
        from database import get_db
        from database.models import Hospital
        
        db = next(get_db())
        hospitals = db.query(Hospital).all()
        
        hospital_data = []
        for hospital in hospitals:
            hospital_data.append({
                'name': hospital.name,
                'id': hospital.id,
                'domain': hospital.domain,
                'api_key': hospital.api_key
            })
        
        db.close()
        return hospital_data
        
    except Exception as e:
        print(f"❌ Failed to get hospitals from database: {e}")
        return []

def test_vector_search():
    """Test vector search functionality"""
    print("\n🧪 Testing Vector Search")
    print("=" * 40)
    
    try:
        from services.enhanced_gemini_service import enhanced_gemini_service
        
        # Test queries for different hospitals
        test_cases = [
            {
                "hospital": "City General Hospital",
                "query": "What are the visiting hours?",
                "expected": "visiting hours"
            },
            {
                "hospital": "Metro Medical Center", 
                "query": "Do you have a cancer center?",
                "expected": "oncology"
            },
            {
                "hospital": "Riverside Community Hospital",
                "query": "What insurance do you accept?",
                "expected": "insurance"
            }
        ]
        
        for test in test_cases:
            print(f"\n🏥 Testing: {test['hospital']}")
            print(f"❓ Query: {test['query']}")
            
            response = enhanced_gemini_service.generate_hospital_response(
                message=test['query'],
                hospital_id=test['hospital']
            )
            
            print(f"🤖 Response: {response[:200]}...")
            
            if test['expected'].lower() in response.lower():
                print("✅ Test passed - relevant information found")
            else:
                print("⚠️  Test unclear - check if response is relevant")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector search test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🏥 Hospital Chatbot SaaS - Vector Database Setup")
    print("=" * 60)
    
    # Check if Qdrant is running
    qdrant_url = os.getenv("QDRANT_URL", "http://localhost:6333")
    print(f"📋 Qdrant URL: {qdrant_url}")
    
    # Setup vector database
    setup_success = setup_vector_database()
    
    if setup_success:
        # Test vector search
        test_success = test_vector_search()
        
        print("\n" + "=" * 60)
        print("📋 SETUP SUMMARY")
        print("=" * 60)
        
        if setup_success and test_success:
            print("🎉 Vector database setup and testing completed successfully!")
            print("\n✅ Your Hospital Chatbot SaaS now has:")
            print("   - Hospital-specific knowledge base")
            print("   - Semantic search capabilities")
            print("   - Context-aware AI responses")
            print("\n🚀 Next steps:")
            print("   1. Start your enhanced server")
            print("   2. Test hospital-specific queries")
            print("   3. Add more hospital documents as needed")
        else:
            print("⚠️  Setup completed with some issues")
    else:
        print("\n❌ Vector database setup failed")
        print("\n💡 Troubleshooting:")
        print("   1. Make sure Qdrant is running: docker-compose up qdrant")
        print("   2. Install dependencies: pip install qdrant-client sentence-transformers")
        print("   3. Check if hospital data exists in PostgreSQL")

if __name__ == "__main__":
    main()
