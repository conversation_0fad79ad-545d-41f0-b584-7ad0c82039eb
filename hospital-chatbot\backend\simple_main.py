"""
Simple Hospital Chatbot SaaS - Basic Server
Minimal version that works without complex imports
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Basic imports
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Hospital Chatbot SaaS API",
    description="A comprehensive SaaS platform for hospital chatbots",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Hospital Chatbot SaaS API",
        "version": "1.0.0",
        "message": "Server is running successfully!"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to Hospital Chatbot SaaS API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "status": "Server is running!",
        "features": [
            "AI-powered chat conversations",
            "Multi-tenant hospital management",
            "Subscription billing",
            "Analytics dashboard",
            "HMS integrations"
        ]
    }

# Test database connection endpoint
@app.get("/test-db")
async def test_database():
    """Test database connection"""
    try:
        from database.database import get_db

        # Try to get a database session
        db = next(get_db())
        db.close()

        return {
            "status": "success",
            "message": "Database connection successful",
            "database": "PostgreSQL"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Database connection failed: {str(e)}",
            "suggestion": "Check your DATABASE_URL in .env file"
        }

# Test environment endpoint
@app.get("/test-env")
async def test_environment():
    """Test environment configuration"""
    try:
        from dotenv import load_dotenv
        load_dotenv()

        # Check critical environment variables
        database_url = os.getenv('DATABASE_URL')
        secret_key = os.getenv('SECRET_KEY')
        gemini_key = os.getenv('GOOGLE_API_KEY')

        # Check if Gemini key is placeholder
        gemini_configured = bool(gemini_key) and gemini_key != "your-gemini-api-key-here"

        return {
            "status": "success",
            "environment": {
                "database_configured": bool(database_url),
                "secret_key_configured": bool(secret_key),
                "gemini_key_configured": gemini_configured,
                "gemini_key_status": "placeholder" if gemini_key == "your-gemini-api-key-here" else ("configured" if gemini_key else "missing"),
                "database_url": database_url[:20] + "..." if database_url else None
            },
            "next_steps": [
                "Get Gemini API key from https://makersuite.google.com/app/apikey" if not gemini_configured else "Gemini API key is configured"
            ]
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Environment check failed: {str(e)}"
        }

# Test Gemini API endpoint
@app.get("/test-gemini")
async def test_gemini():
    """Test Gemini API connection"""
    try:
        from dotenv import load_dotenv
        load_dotenv()

        gemini_key = os.getenv('GOOGLE_API_KEY')

        if not gemini_key or gemini_key == "your-gemini-api-key-here":
            return {
                "status": "error",
                "message": "Gemini API key not configured",
                "instructions": "Get your API key from https://makersuite.google.com/app/apikey and update GOOGLE_API_KEY in .env file"
            }

        # Try to import and test Gemini
        import google.generativeai as genai
        genai.configure(api_key=gemini_key)

        # Test with a simple prompt
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Say 'Hello from Gemini!'")

        return {
            "status": "success",
            "message": "Gemini API is working!",
            "test_response": response.text,
            "model": "gemini-2.0-flash-exp"
        }

    except ImportError:
        return {
            "status": "error",
            "message": "google-generativeai package not installed",
            "solution": "Run: pip install google-generativeai"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Gemini API test failed: {str(e)}",
            "suggestion": "Check your API key and internet connection"
        }

# Simple chat endpoint (without complex dependencies)
@app.post("/api/simple-chat")
async def simple_chat(message: dict):
    """Simple chat endpoint for testing"""
    user_message = message.get("message", "")

    # Simple response logic
    if "hello" in user_message.lower():
        response = "Hello! Welcome to our hospital. How can I help you today?"
    elif "appointment" in user_message.lower():
        response = "I can help you book an appointment. What department would you like to visit?"
    elif "emergency" in user_message.lower():
        response = "For emergencies, please call 911 or visit our Emergency Department immediately."
    else:
        response = "Thank you for your message. Our team will assist you shortly."

    return {
        "response": response,
        "user_message": user_message,
        "timestamp": str(os.times()),
        "status": "success"
    }

if __name__ == "__main__":
    print("🏥 Hospital Chatbot SaaS - Simple Server")
    print("=" * 50)

    # Check environment
    env_file = current_dir / ".env"
    if env_file.exists():
        print("✅ Environment file found")
    else:
        print("⚠️  .env file not found - using defaults")

    print("🚀 Starting simple server...")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("🧪 Test Database: http://localhost:8000/test-db")
    print("⚙️  Test Environment: http://localhost:8000/test-env")
    print("💬 Simple Chat: POST to http://localhost:8000/api/simple-chat")
    print()

    try:
        uvicorn.run(
            "simple_main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        print("💡 Make sure all dependencies are installed: pip install fastapi uvicorn python-dotenv")
