#!/usr/bin/env python3
"""
Test Database Connection
Simple script to test database connectivity and setup
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables
load_dotenv()

def test_database_connection():
    """Test database connection"""
    print("🗄️  Testing Database Connection")
    print("=" * 50)
    
    # Check environment variable
    database_url = os.getenv('DATABASE_URL')
    print(f"📋 DATABASE_URL: {database_url}")
    
    if not database_url:
        print("❌ DATABASE_URL not found in environment")
        return False
    
    if database_url.startswith('postgresql://'):
        print("✅ PostgreSQL database detected")
        return test_postgresql_connection(database_url)
    elif database_url.startswith('sqlite://'):
        print("✅ SQLite database detected")
        return test_sqlite_connection(database_url)
    else:
        print(f"⚠️  Unknown database type: {database_url}")
        return False

def test_postgresql_connection(database_url):
    """Test PostgreSQL connection"""
    try:
        import psycopg2
        print("✅ psycopg2 package available")
        
        # Test direct connection
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL connection successful")
        print(f"📊 Version: {version}")
        
        # Test if our database exists
        cursor.execute("SELECT current_database();")
        db_name = cursor.fetchone()[0]
        print(f"🗄️  Connected to database: {db_name}")
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("❌ psycopg2 package not installed")
        print("💡 Install with: pip install psycopg2-binary")
        return False
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("💡 Check if PostgreSQL is running and database exists")
        return False

def test_sqlite_connection(database_url):
    """Test SQLite connection"""
    try:
        import sqlite3
        
        # Extract file path from URL
        db_path = database_url.replace('sqlite:///', '')
        print(f"📁 SQLite file: {db_path}")
        
        # Test connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        version = cursor.fetchone()[0]
        print(f"✅ SQLite connection successful")
        print(f"📊 Version: {version}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ SQLite connection failed: {e}")
        return False

def test_sqlalchemy_models():
    """Test SQLAlchemy models"""
    print("\n🏗️  Testing SQLAlchemy Models")
    print("=" * 50)
    
    try:
        from database import engine, Base, init_db
        print("✅ Database modules imported successfully")
        
        # Check if we're using the right database
        database_url = str(engine.url)
        print(f"📋 SQLAlchemy engine URL: {database_url}")
        
        if database_url.startswith('sqlite'):
            print("⚠️  Using SQLite - UUID columns may cause issues")
            print("💡 Consider using PostgreSQL for production")
        
        # Test table creation
        print("🏗️  Creating database tables...")
        init_db()
        print("✅ Tables created successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ SQLAlchemy test failed: {e}")
        return False

def test_sample_data():
    """Test if sample data exists"""
    print("\n📊 Testing Sample Data")
    print("=" * 50)
    
    try:
        from database import get_db
        from database.models import Hospital, User
        
        db = next(get_db())
        
        # Check hospitals
        hospital_count = db.query(Hospital).count()
        print(f"🏥 Hospitals in database: {hospital_count}")
        
        # Check users
        user_count = db.query(User).count()
        print(f"👥 Users in database: {user_count}")
        
        if hospital_count > 0:
            # Show first hospital
            first_hospital = db.query(Hospital).first()
            print(f"📋 Sample hospital: {first_hospital.name}")
            print(f"🔑 API Key: {first_hospital.api_key}")
        
        db.close()
        
        if hospital_count > 0:
            print("✅ Sample data is available!")
            return True
        else:
            print("⚠️  No sample data found")
            print("💡 Run sample data setup: cd sample && python setup_complete.py")
            return False
            
    except Exception as e:
        print(f"❌ Sample data test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🏥 Hospital Chatbot SaaS - Database Test")
    print("=" * 60)
    
    # Test database connection
    connection_ok = test_database_connection()
    
    if connection_ok:
        # Test SQLAlchemy models
        models_ok = test_sqlalchemy_models()
        
        if models_ok:
            # Test sample data
            data_ok = test_sample_data()
            
            print("\n" + "=" * 60)
            print("📋 DATABASE TEST SUMMARY")
            print("=" * 60)
            
            if connection_ok and models_ok and data_ok:
                print("🎉 All database tests passed!")
                print("✅ Your database is ready for the Hospital Chatbot SaaS")
            elif connection_ok and models_ok:
                print("✅ Database connection and models work!")
                print("⚠️  Consider adding sample data for testing")
            else:
                print("⚠️  Some database tests failed")
        else:
            print("\n❌ Database model tests failed")
    else:
        print("\n❌ Database connection test failed")
        print("💡 Check your DATABASE_URL in .env file")

if __name__ == "__main__":
    main()
