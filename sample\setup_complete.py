#!/usr/bin/env python3
"""
Complete Setup Script for Hospital Chatbot SaaS Database
Creates database, tables, and populates with dummy data
"""

import sys
import os
from database_creator import DatabaseCreator
from data_generator import DataGenerator

def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        'psycopg2', 'sqlalchemy', 'faker', 'bcrypt', 'tqdm'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed")
    return True

def check_postgresql():
    """Check if PostgreSQL is accessible"""
    print("🔍 Checking PostgreSQL accessibility...")
    
    try:
        import psycopg2
        # Try to connect to default postgres database
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            user="postgres",
            database="postgres"
        )
        conn.close()
        print("✅ PostgreSQL is accessible")
        return True
    except Exception as e:
        print(f"❌ Cannot connect to PostgreSQL: {e}")
        print("Please ensure PostgreSQL is installed and running")
        return False

def main():
    """Main setup function"""
    print("🏥 Hospital Chatbot SaaS - Complete Database Setup")
    print("=" * 60)
    print("This script will:")
    print("1. Check dependencies and PostgreSQL")
    print("2. Create database and user")
    print("3. Create all tables")
    print("4. Generate and insert dummy data")
    print("=" * 60)
    
    # Get user confirmation
    response = input("\nProceed with setup? (y/N): ")
    if response.lower() != 'y':
        print("Setup cancelled.")
        sys.exit(0)
    
    # Step 1: Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Step 2: Check PostgreSQL
    if not check_postgresql():
        print("\n💡 PostgreSQL Setup Tips:")
        print("   - Install PostgreSQL from https://postgresql.org/download/")
        print("   - Start PostgreSQL service")
        print("   - Ensure default 'postgres' user is accessible")
        sys.exit(1)
    
    # Step 3: Create database and tables
    print("\n" + "=" * 60)
    print("STEP 1: DATABASE CREATION")
    print("=" * 60)
    
    creator = DatabaseCreator()
    if not creator.setup_complete_database():
        print("❌ Database setup failed")
        sys.exit(1)
    
    # Step 4: Generate dummy data
    print("\n" + "=" * 60)
    print("STEP 2: DUMMY DATA GENERATION")
    print("=" * 60)
    
    try:
        generator = DataGenerator()
        generator.generate_all_data()
    except Exception as e:
        print(f"❌ Data generation failed: {e}")
        sys.exit(1)
    
    # Step 5: Final summary
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    print("\n📋 Database Information:")
    print("   Host: localhost")
    print("   Port: 5432")
    print("   Database: hospital_chatbot_saas")
    print("   Username: hospital_chatbot_user")
    print("   Password: HospitalBot2024!")
    
    print("\n🏥 Sample Hospitals Created:")
    print("   1. City General Hospital (Premium)")
    print("   2. Children's Medical Center (Enterprise)")
    print("   3. Heart & Vascular Institute (Basic)")
    print("   4. Metro Emergency Hospital (Premium)")
    print("   5. Women's Health Clinic (Trial)")
    print("   ... and 5 more hospitals")
    
    print("\n👥 Sample Users:")
    print("   Admin users: admin@[hospital-domain].com / admin123")
    print("   Regular users: user1@[hospital-domain].com / user123")
    
    print("\n📊 Generated Data:")
    print("   - 10 Hospitals with different subscription plans")
    print("   - 50 Users (5 per hospital)")
    print("   - 500 Patients (50 per hospital)")
    print("   - 1,000 Appointments")
    print("   - 2,000 Chat Sessions with 10,000+ messages")
    print("   - 90 days of usage metrics")
    print("   - Subscription history and billing data")
    
    print("\n🚀 Next Steps:")
    print("1. Navigate to your main hospital-chatbot project")
    print("2. Update backend/.env with these database credentials:")
    print("   DATABASE_URL=postgresql://hospital_chatbot_user:HospitalBot2024!@localhost:5432/hospital_chatbot_saas")
    print("3. Start your application:")
    print("   cd hospital-chatbot/backend")
    print("   uvicorn api.main:app --reload")
    print("4. Test the API at: http://localhost:8000/docs")
    
    print("\n🧪 Testing the Setup:")
    print("You can test the database connection with:")
    print("   python -c \"from config import get_db_connection; print('✅ Connected!' if get_db_connection() else '❌ Failed')\"")
    
    print("\n📚 Documentation:")
    print("   - API Documentation: http://localhost:8000/docs")
    print("   - Sample data details: Check the generated hospitals and their API keys")
    print("   - Database schema: All tables are created and populated")

if __name__ == "__main__":
    main()
