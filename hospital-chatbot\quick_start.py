#!/usr/bin/env python3
"""
Quick Start Script for Hospital Chatbot SaaS
Tests the application setup and provides sample API calls
"""

import os
import sys
import requests
import json
from datetime import datetime

def check_environment():
    """Check if environment is properly configured"""
    print("🔍 Checking Environment Configuration...")
    
    env_file = os.path.join(os.path.dirname(__file__), 'backend', '.env')
    if not os.path.exists(env_file):
        print("❌ .env file not found in backend directory")
        return False
    
    # Check critical environment variables
    critical_vars = [
        'DATABASE_URL',
        'SECRET_KEY'
    ]
    
    missing_vars = []
    with open(env_file, 'r') as f:
        env_content = f.read()
        for var in critical_vars:
            if f"{var}=" not in env_content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing critical environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment configuration looks good")
    return True

def check_database():
    """Check database connection"""
    print("🗄️  Checking Database Connection...")
    
    try:
        # Import here to avoid issues if modules aren't installed
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        from database.database import get_db
        
        # Try to get a database session
        db = next(get_db())
        db.close()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Make sure PostgreSQL is running and database exists")
        return False

def check_api_server():
    """Check if API server is running"""
    print("🌐 Checking API Server...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"❌ API server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ API server is not running")
        print("💡 Start with: cd backend && uvicorn api.main:app --reload")
        return False
    except Exception as e:
        print(f"❌ Error checking API server: {e}")
        return False

def get_sample_hospital_data():
    """Get sample hospital data from database"""
    print("🏥 Getting Sample Hospital Data...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'sample'))
        from config import get_database_url
        from sqlalchemy import create_engine, text
        
        engine = create_engine(get_database_url())
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT name, api_key, subscription_plan, contact_email
                FROM hospitals 
                ORDER BY name 
                LIMIT 3
            """))
            
            hospitals = result.fetchall()
            
            if hospitals:
                print(f"✅ Found {len(hospitals)} sample hospitals:")
                for i, (name, api_key, plan, email) in enumerate(hospitals, 1):
                    print(f"   {i}. {name} ({plan})")
                    print(f"      API Key: {api_key}")
                    print(f"      Contact: {email}")
                    print()
                return hospitals[0]  # Return first hospital for testing
            else:
                print("❌ No sample hospitals found")
                print("💡 Run: cd sample && python setup_complete.py")
                return None
                
    except Exception as e:
        print(f"❌ Error getting sample data: {e}")
        return None

def test_chat_api(hospital_data):
    """Test the chat API"""
    if not hospital_data:
        return False
    
    print("💬 Testing Chat API...")
    
    name, api_key, plan, email = hospital_data
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "message": "Hello, I need help booking an appointment",
            "visitor_id": f"test_visitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        response = requests.post(
            "http://localhost:8000/api/chat/message",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat API test successful")
            print(f"   Bot Response: {result.get('response', 'No response')}")
            print(f"   Session ID: {result.get('session_id', 'No session')}")
            return True
        else:
            print(f"❌ Chat API test failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat API test error: {e}")
        return False

def test_auth_api(hospital_data):
    """Test the authentication API"""
    if not hospital_data:
        return False
    
    print("🔐 Testing Authentication API...")
    
    name, api_key, plan, email = hospital_data
    
    try:
        data = {
            "username": email,
            "password": "admin123"
        }
        
        response = requests.post(
            "http://localhost:8000/api/auth/login",
            data=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Authentication test successful")
            print(f"   Token type: {result.get('token_type', 'Unknown')}")
            return result.get('access_token')
        else:
            print(f"❌ Authentication test failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Authentication test error: {e}")
        return None

def test_dashboard_api(access_token):
    """Test the dashboard API"""
    if not access_token:
        return False
    
    print("📊 Testing Dashboard API...")
    
    try:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            "http://localhost:8000/api/dashboard/stats",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Dashboard API test successful")
            print(f"   Total conversations: {result.get('total_conversations', 0)}")
            print(f"   Total messages: {result.get('total_messages', 0)}")
            return True
        else:
            print(f"❌ Dashboard API test failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard API test error: {e}")
        return False

def main():
    """Main quick start function"""
    print("🏥 Hospital Chatbot SaaS - Quick Start Test")
    print("=" * 60)
    
    # Step 1: Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix configuration.")
        sys.exit(1)
    
    # Step 2: Check database
    if not check_database():
        print("\n❌ Database check failed. Please check database setup.")
        sys.exit(1)
    
    # Step 3: Check API server
    if not check_api_server():
        print("\n❌ API server check failed. Please start the server.")
        sys.exit(1)
    
    # Step 4: Get sample data
    hospital_data = get_sample_hospital_data()
    if not hospital_data:
        print("\n❌ No sample data found. Please run sample data setup.")
        sys.exit(1)
    
    # Step 5: Test APIs
    print("\n" + "=" * 60)
    print("🧪 TESTING APIs")
    print("=" * 60)
    
    # Test chat API
    chat_success = test_chat_api(hospital_data)
    
    # Test authentication
    access_token = test_auth_api(hospital_data)
    
    # Test dashboard (if auth successful)
    dashboard_success = False
    if access_token:
        dashboard_success = test_dashboard_api(access_token)
    
    # Final summary
    print("\n" + "=" * 60)
    print("📋 QUICK START SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Environment", True),
        ("Database", True),
        ("API Server", True),
        ("Sample Data", True),
        ("Chat API", chat_success),
        ("Authentication", bool(access_token)),
        ("Dashboard API", dashboard_success)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name:15}: {status}")
    
    print(f"\n🎯 Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! Your Hospital Chatbot SaaS is ready!")
        print("\n🚀 Next steps:")
        print("   1. Get Gemini API key: https://makersuite.google.com/app/apikey")
        print("   2. Update GOOGLE_API_KEY in backend/.env")
        print("   3. Test advanced AI features")
        print("   4. Set up Stripe for billing (optional)")
        print("   5. Deploy to production")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
