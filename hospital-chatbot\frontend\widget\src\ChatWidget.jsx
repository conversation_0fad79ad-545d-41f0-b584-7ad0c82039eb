import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import './ChatWidget.css';

const ChatWidget = ({ hospitalId, apiKey }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  const sendMessage = async (message) => {
    if (!message.trim()) return;

    // Add user message
    const userMessage = { text: message, sender: 'user', timestamp: new Date() };
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    try {
      const response = await axios.post('/api/chat/message', {
        message,
        hospital_id: hospitalId,
        session_id: sessionStorage.getItem('chat_session_id')
      }, {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });

      // Add bot response
      const botMessage = {
        text: response.data.response,
        sender: 'bot',
        timestamp: new Date(),
        intent: response.data.intent
      };
      
      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  return (
    <div className={`chat-widget ${isOpen ? 'open' : 'closed'}`}>
      <div className="chat-header" onClick={() => setIsOpen(!isOpen)}>
        <span>💬 Chat with us</span>
      </div>
      
      {isOpen && (
        <div className="chat-body">
          <div className="messages-container">
            {messages.map((msg, index) => (
              <div key={index} className={`message ${msg.sender}`}>
                <div className="message-content">{msg.text}</div>
                <div className="message-time">
                  {msg.timestamp.toLocaleTimeString()}
                </div>
              </div>
            ))}
            {isTyping && <div className="typing-indicator">Bot is typing...</div>}
            <div ref={messagesEndRef} />
          </div>
          
          <div className="input-container">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage(inputMessage)}
              placeholder="Type your message..."
            />
            <button onClick={() => sendMessage(inputMessage)}>Send</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatWidget;