"""
Gemini 2.0 Flash AI Service for Hospital Chatbot SaaS
Provides advanced AI capabilities for medical conversations, symptom analysis, and intelligent responses
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class GeminiService:
    def __init__(self):
        """Initialize Gemini AI service"""
        # Load environment variables again to ensure they're available
        load_dotenv()

        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp")

        # Debug: Print API key status (first 10 chars only for security)
        if self.api_key:
            print(f"✅ Gemini API key loaded: {self.api_key[:10]}...")
        else:
            print("❌ Gemini API key not found in environment")

        if not self.api_key or self.api_key == "your-gemini-api-key-here":
            raise ValueError("GOOGLE_API_KEY environment variable is required. Get your key from https://makersuite.google.com/app/apikey")

        # Configure Gemini
        genai.configure(api_key=self.api_key)

        # Initialize model with safety settings (using valid categories only)
        try:
            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                safety_settings={
                    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                }
            )
            print(f"✅ Gemini model initialized: {self.model_name}")
        except Exception as e:
            print(f"⚠️  Error with safety settings, trying without: {e}")
            # Fallback: Initialize without safety settings
            self.model = genai.GenerativeModel(model_name=self.model_name)
            print(f"✅ Gemini model initialized (no safety settings): {self.model_name}")

        # Hospital-specific system prompts
        self.system_prompts = {
            "general": self._get_general_system_prompt(),
            "symptom_checker": self._get_symptom_checker_prompt(),
            "appointment_booking": self._get_appointment_booking_prompt(),
            "emergency_triage": self._get_emergency_triage_prompt(),
            "insurance_help": self._get_insurance_help_prompt()
        }

    def _get_general_system_prompt(self) -> str:
        """General hospital assistant system prompt"""
        return """You are a helpful AI assistant for a hospital chatbot SaaS platform. Your role is to:

1. Assist patients with general hospital information
2. Help with appointment scheduling
3. Provide basic health information (NOT medical diagnosis)
4. Guide users to appropriate departments
5. Handle insurance and billing inquiries
6. Provide emergency contact information when needed

IMPORTANT GUIDELINES:
- Never provide medical diagnoses or treatment advice
- Always recommend consulting healthcare professionals for medical concerns
- Be empathetic and professional
- Respect patient privacy and confidentiality
- For emergencies, immediately direct to emergency services
- Keep responses concise but helpful
- Use simple, clear language

You represent the hospital's commitment to excellent patient care and service."""

    def _get_symptom_checker_prompt(self) -> str:
        """Symptom checker system prompt"""
        return """You are a symptom assessment assistant for a hospital chatbot. Your role is to:

1. Gather symptom information from patients
2. Assess urgency level (Emergency, Urgent, Routine)
3. Recommend appropriate care level
4. Suggest relevant hospital departments

CRITICAL RULES:
- NEVER diagnose medical conditions
- NEVER recommend specific treatments or medications
- ALWAYS emphasize the need for professional medical evaluation
- For severe symptoms, immediately recommend emergency care
- Use a structured approach to gather symptom information

URGENCY LEVELS:
- Emergency: Life-threatening symptoms (chest pain, difficulty breathing, severe bleeding, etc.)
- Urgent: Symptoms needing same-day care (high fever, severe pain, etc.)
- Routine: Symptoms that can wait for regular appointment

Always end with: "This assessment is not a medical diagnosis. Please consult with a healthcare professional for proper evaluation and treatment."""

    def _get_appointment_booking_prompt(self) -> str:
        """Appointment booking assistant prompt"""
        return """You are an appointment booking assistant for a hospital. Your role is to:

1. Help patients schedule appointments
2. Gather necessary information (name, contact, preferred dates/times)
3. Suggest appropriate departments based on needs
4. Explain appointment procedures and preparation
5. Handle appointment modifications and cancellations

INFORMATION TO COLLECT:
- Patient name and contact information
- Reason for visit
- Preferred department/specialist
- Preferred date and time
- Insurance information
- Any special requirements

Be efficient but thorough in gathering information. Confirm all details before finalizing appointments."""

    def _get_emergency_triage_prompt(self) -> str:
        """Emergency triage system prompt"""
        return """You are an emergency triage assistant. Your role is to:

1. Quickly assess emergency situations
2. Provide immediate guidance for life-threatening conditions
3. Direct patients to appropriate emergency services

IMMEDIATE EMERGENCY SIGNS (Call 911 immediately):
- Chest pain or pressure
- Difficulty breathing or shortness of breath
- Severe bleeding
- Loss of consciousness
- Severe allergic reactions
- Signs of stroke (face drooping, arm weakness, speech difficulty)
- Severe burns
- Suspected poisoning

For ANY emergency symptoms, immediately provide:
1. "Call 911 immediately" or "Go to the nearest emergency room"
2. Basic first aid instructions if safe to provide
3. Hospital emergency department contact information

Never delay emergency care for information gathering."""

    def _get_insurance_help_prompt(self) -> str:
        """Insurance assistance system prompt"""
        return """You are an insurance and billing assistant for a hospital. Your role is to:

1. Help patients understand insurance coverage
2. Explain billing procedures
3. Assist with insurance verification
4. Provide financial assistance information
5. Handle billing inquiries

COMMON TOPICS:
- Insurance plan acceptance
- Coverage verification
- Copay and deductible information
- Prior authorization requirements
- Payment plans and financial assistance
- Billing statement explanations

Always be patient and clear when explaining insurance matters. Offer to connect patients with financial counselors for complex situations."""

    async def generate_response(
        self,
        message: str,
        context: Dict[str, Any],
        intent: Optional[str] = None,
        hospital_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate AI response using Gemini"""
        try:
            # Select appropriate system prompt based on intent
            system_prompt = self._select_system_prompt(intent)

            # Build context-aware prompt
            full_prompt = self._build_prompt(
                message, context, system_prompt, hospital_info
            )

            # Generate response
            response = await self._generate_with_retry(full_prompt)

            # Process and structure response
            return self._process_response(response, intent)

        except Exception as e:
            logger.error(f"Error generating Gemini response: {e}")
            return {
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again or contact our support team.",
                "intent": "fallback",
                "confidence": 0.0,
                "suggestions": ["Contact support", "Try again later"]
            }

    def _select_system_prompt(self, intent: Optional[str]) -> str:
        """Select appropriate system prompt based on intent"""
        if intent in ["check_symptoms", "symptom_checker"]:
            return self.system_prompts["symptom_checker"]
        elif intent in ["book_appointment", "appointment_booking"]:
            return self.system_prompts["appointment_booking"]
        elif intent in ["emergency_contact", "emergency"]:
            return self.system_prompts["emergency_triage"]
        elif intent in ["insurance_query", "billing"]:
            return self.system_prompts["insurance_help"]
        else:
            return self.system_prompts["general"]

    def _build_prompt(
        self,
        message: str,
        context: Dict[str, Any],
        system_prompt: str,
        hospital_info: Optional[Dict[str, Any]]
    ) -> str:
        """Build comprehensive prompt with context"""

        # Hospital information
        hospital_context = ""
        if hospital_info:
            hospital_context = f"""
HOSPITAL INFORMATION:
- Name: {hospital_info.get('name', 'Our Hospital')}
- Departments: {', '.join(hospital_info.get('departments', ['General Medicine']))}
- Emergency Contact: {hospital_info.get('emergency_contact', '911')}
- Visiting Hours: {hospital_info.get('visiting_hours', 'Please contact us for current hours')}
"""

        # Conversation context
        conversation_context = ""
        if context.get('previous_messages'):
            conversation_context = "CONVERSATION HISTORY:\n"
            for msg in context['previous_messages'][-3:]:  # Last 3 messages
                conversation_context += f"- {msg['sender']}: {msg['message']}\n"

        # Current user message
        current_message = f"\nCURRENT USER MESSAGE: {message}"

        # Build full prompt
        full_prompt = f"""
{system_prompt}

{hospital_context}

{conversation_context}

{current_message}

Please provide a helpful, professional response following the guidelines above.
"""

        return full_prompt

    async def _generate_with_retry(self, prompt: str, max_retries: int = 3) -> str:
        """Generate response with retry logic"""
        for attempt in range(max_retries):
            try:
                response = self.model.generate_content(prompt)
                return response.text
            except Exception as e:
                logger.warning(f"Gemini generation attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise

        raise Exception("Max retries exceeded for Gemini generation")

    def _process_response(self, response: str, intent: Optional[str]) -> Dict[str, Any]:
        """Process and structure the AI response"""

        # Generate suggestions based on intent and response
        suggestions = self._generate_suggestions(response, intent)

        # Determine confidence (simplified logic)
        confidence = 0.9 if len(response) > 50 else 0.7

        return {
            "response": response.strip(),
            "intent": intent or "general",
            "confidence": confidence,
            "suggestions": suggestions,
            "source": "gemini-2.0-flash"
        }

    def _generate_suggestions(self, response: str, intent: Optional[str]) -> List[str]:
        """Generate contextual suggestions"""
        base_suggestions = [
            "Book an appointment",
            "Contact information",
            "Emergency services"
        ]

        intent_suggestions = {
            "check_symptoms": [
                "Book appointment with doctor",
                "Emergency contact",
                "Find specialist"
            ],
            "book_appointment": [
                "Check available times",
                "Department information",
                "Insurance verification"
            ],
            "insurance_query": [
                "Verify coverage",
                "Payment options",
                "Financial assistance"
            ]
        }

        return intent_suggestions.get(intent, base_suggestions)

    async def analyze_sentiment(self, message: str) -> Dict[str, Any]:
        """Analyze sentiment and urgency of patient message"""
        prompt = f"""
Analyze the following patient message for:
1. Sentiment (positive, neutral, negative, urgent)
2. Urgency level (low, medium, high, emergency)
3. Emotional state (calm, worried, anxious, distressed, angry)
4. Key concerns mentioned

Patient message: "{message}"

Respond in JSON format:
{{
    "sentiment": "...",
    "urgency": "...",
    "emotional_state": "...",
    "key_concerns": ["...", "..."],
    "requires_human_intervention": true/false
}}
"""

        try:
            response = await self._generate_with_retry(prompt)
            # Parse JSON response
            return json.loads(response)
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return {
                "sentiment": "neutral",
                "urgency": "medium",
                "emotional_state": "unknown",
                "key_concerns": [],
                "requires_human_intervention": False
            }

# Global instance
gemini_service = GeminiService()
