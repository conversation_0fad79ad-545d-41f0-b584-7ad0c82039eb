#!/usr/bin/env python3
"""
PostgreSQL setup script for Hospital Chatbot SaaS
This script helps configure PostgreSQL for local development and production
"""

import os
import sys
import subprocess
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import getpass

def check_postgresql_service():
    """Check if PostgreSQL service is running"""
    try:
        # For Windows
        result = subprocess.run(['sc', 'query', 'postgresql-x64-15'], 
                              capture_output=True, text=True)
        if 'RUNNING' in result.stdout:
            return True
        
        # Try alternative service names
        for service in ['postgresql-15', 'postgresql']:
            result = subprocess.run(['sc', 'query', service], 
                                  capture_output=True, text=True)
            if 'RUNNING' in result.stdout:
                return True
        
        return False
    except:
        # For Linux/Mac
        try:
            result = subprocess.run(['systemctl', 'is-active', 'postgresql'], 
                                  capture_output=True, text=True)
            return result.stdout.strip() == 'active'
        except:
            return False

def start_postgresql_service():
    """Start PostgreSQL service"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['net', 'start', 'postgresql-x64-15'], check=True)
        else:  # Linux/Mac
            subprocess.run(['sudo', 'systemctl', 'start', 'postgresql'], check=True)
        print("✅ PostgreSQL service started successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start PostgreSQL service: {e}")
        return False

def create_database_and_user():
    """Create database and user for the application"""
    print("\n🔧 Setting up PostgreSQL database and user...")
    
    # Get PostgreSQL superuser credentials
    print("Please provide PostgreSQL superuser credentials:")
    pg_user = input("PostgreSQL username (default: postgres): ").strip() or "postgres"
    pg_password = getpass.getpass("PostgreSQL password: ")
    pg_host = input("PostgreSQL host (default: localhost): ").strip() or "localhost"
    pg_port = input("PostgreSQL port (default: 5432): ").strip() or "5432"
    
    try:
        # Connect as superuser
        conn = psycopg2.connect(
            host=pg_host,
            port=pg_port,
            user=pg_user,
            password=pg_password,
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create application database
        db_name = "hospital_chatbot_saas"
        try:
            cursor.execute(f"CREATE DATABASE {db_name};")
            print(f"✅ Database '{db_name}' created successfully")
        except psycopg2.errors.DuplicateDatabase:
            print(f"ℹ️  Database '{db_name}' already exists")
        
        # Create application user
        app_user = "hospital_chatbot_user"
        app_password = "HospitalBot2024!"
        
        try:
            cursor.execute(f"CREATE USER {app_user} WITH PASSWORD '{app_password}';")
            print(f"✅ User '{app_user}' created successfully")
        except psycopg2.errors.DuplicateObject:
            print(f"ℹ️  User '{app_user}' already exists")
            # Update password
            cursor.execute(f"ALTER USER {app_user} WITH PASSWORD '{app_password}';")
            print(f"✅ Password updated for user '{app_user}'")
        
        # Grant privileges
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {db_name} TO {app_user};")
        cursor.execute(f"ALTER USER {app_user} CREATEDB;")  # Allow creating test databases
        
        # Connect to the new database and grant schema privileges
        conn.close()
        
        conn = psycopg2.connect(
            host=pg_host,
            port=pg_port,
            user=pg_user,
            password=pg_password,
            database=db_name
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        cursor.execute(f"GRANT ALL ON SCHEMA public TO {app_user};")
        cursor.execute(f"GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO {app_user};")
        cursor.execute(f"GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO {app_user};")
        cursor.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO {app_user};")
        cursor.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO {app_user};")
        
        print("✅ All privileges granted successfully")
        
        # Create .env file
        create_env_file(pg_host, pg_port, db_name, app_user, app_password)
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False

def create_env_file(host, port, db_name, user, password):
    """Create .env file with database configuration"""
    env_content = f"""# Database Configuration for SaaS
DATABASE_URL=postgresql://{user}:{password}@{host}:{port}/{db_name}

# Security - CHANGE THESE IN PRODUCTION!
SECRET_KEY=your-super-secret-key-change-in-production-make-it-very-long-and-random-for-saas
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Gemini AI Configuration
GOOGLE_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-2.0-flash-exp

# Rasa Configuration
RASA_URL=http://localhost:5005
RASA_ACTION_ENDPOINT=http://localhost:5055/webhook

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=False
ENVIRONMENT=development

# CORS Configuration - Update for production
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://yourdomain.com

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Email Configuration (for SaaS notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# SaaS Configuration
SAAS_DOMAIN=yourdomain.com
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn-for-error-tracking
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Logging
LOG_LEVEL=INFO
LOG_FILE=hospital_chatbot_saas.log

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# File Storage (for SaaS)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=hospital-chatbot-saas-files
AWS_REGION=us-east-1
"""
    
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend', '.env')
    
    with open(env_path, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Environment file created at: {env_path}")
    print("⚠️  Please update the API keys and secrets in the .env file!")

def main():
    """Main setup function"""
    print("🏥 Hospital Chatbot SaaS - PostgreSQL Setup")
    print("=" * 50)
    
    # Check if PostgreSQL is running
    if not check_postgresql_service():
        print("❌ PostgreSQL service is not running")
        print("🔄 Attempting to start PostgreSQL service...")
        if not start_postgresql_service():
            print("❌ Failed to start PostgreSQL. Please start it manually and run this script again.")
            sys.exit(1)
    else:
        print("✅ PostgreSQL service is running")
    
    # Create database and user
    if create_database_and_user():
        print("\n🎉 PostgreSQL setup completed successfully!")
        print("\nNext steps:")
        print("1. Update the .env file with your API keys")
        print("2. Run: cd backend && python scripts/init_db.py")
        print("3. Start the application: uvicorn api.main:app --reload")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
