"""
Database Creator for Hospital Chatbot SaaS
Creates PostgreSQL database, user, and tables
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine
import sys
import getpass
from config import DATABASE_CONFIG, get_database_url
from models import Base

class DatabaseCreator:
    def __init__(self):
        self.config = DATABASE_CONFIG
        
    def create_database_and_user(self):
        """Create PostgreSQL database and user"""
        print("🔧 Creating PostgreSQL database and user...")
        
        # Get superuser password
        superuser_password = self.config.get('superuser_password')
        if not superuser_password:
            superuser_password = getpass.getpass(
                f"Enter password for PostgreSQL superuser '{self.config['superuser']}': "
            )
        
        try:
            # Connect as superuser
            conn = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['superuser'],
                password=superuser_password,
                database='postgres'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # Create database
            db_name = self.config['database']
            try:
                cursor.execute(f'CREATE DATABASE "{db_name}";')
                print(f"✅ Database '{db_name}' created successfully")
            except psycopg2.errors.DuplicateDatabase:
                print(f"ℹ️  Database '{db_name}' already exists")
            
            # Create user
            username = self.config['username']
            password = self.config['password']
            
            try:
                cursor.execute(f"CREATE USER {username} WITH PASSWORD '{password}';")
                print(f"✅ User '{username}' created successfully")
            except psycopg2.errors.DuplicateObject:
                print(f"ℹ️  User '{username}' already exists")
                # Update password
                cursor.execute(f"ALTER USER {username} WITH PASSWORD '{password}';")
                print(f"✅ Password updated for user '{username}'")
            
            # Grant privileges
            cursor.execute(f'GRANT ALL PRIVILEGES ON DATABASE "{db_name}" TO {username};')
            cursor.execute(f"ALTER USER {username} CREATEDB;")
            
            cursor.close()
            conn.close()
            
            # Connect to the new database and grant schema privileges
            conn = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['superuser'],
                password=superuser_password,
                database=db_name
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # Grant schema privileges
            cursor.execute(f"GRANT ALL ON SCHEMA public TO {username};")
            cursor.execute(f"GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO {username};")
            cursor.execute(f"GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO {username};")
            cursor.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO {username};")
            cursor.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO {username};")
            
            print("✅ All database privileges granted successfully")
            
            cursor.close()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating database: {e}")
            return False
    
    def create_tables(self):
        """Create all database tables"""
        print("🏗️  Creating database tables...")
        
        try:
            # Create SQLAlchemy engine
            engine = create_engine(get_database_url())
            
            # Create all tables
            Base.metadata.create_all(bind=engine)
            
            print("✅ All tables created successfully!")
            
            # Print created tables
            inspector = engine.inspect(engine)
            tables = inspector.get_table_names()
            print(f"📋 Created {len(tables)} tables:")
            for table in sorted(tables):
                print(f"   - {table}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating tables: {e}")
            return False
    
    def verify_setup(self):
        """Verify database setup"""
        print("🔍 Verifying database setup...")
        
        try:
            # Test connection
            engine = create_engine(get_database_url())
            with engine.connect() as conn:
                result = conn.execute("SELECT version();")
                version = result.fetchone()[0]
                print(f"✅ Database connection successful")
                print(f"📊 PostgreSQL version: {version}")
            
            # Check tables
            inspector = engine.inspect(engine)
            tables = inspector.get_table_names()
            
            expected_tables = [
                'hospitals', 'users', 'chat_sessions', 'chat_messages',
                'appointments', 'patients', 'integration_settings',
                'subscription_history', 'usage_metrics', 'audit_logs',
                'api_keys', 'notification_templates', 'system_configurations'
            ]
            
            missing_tables = set(expected_tables) - set(tables)
            if missing_tables:
                print(f"⚠️  Missing tables: {', '.join(missing_tables)}")
                return False
            
            print(f"✅ All {len(expected_tables)} required tables exist")
            return True
            
        except Exception as e:
            print(f"❌ Database verification failed: {e}")
            return False
    
    def setup_complete_database(self):
        """Complete database setup process"""
        print("🏥 Hospital Chatbot SaaS - Database Setup")
        print("=" * 50)
        
        # Step 1: Create database and user
        if not self.create_database_and_user():
            print("❌ Failed to create database and user")
            return False
        
        # Step 2: Create tables
        if not self.create_tables():
            print("❌ Failed to create tables")
            return False
        
        # Step 3: Verify setup
        if not self.verify_setup():
            print("❌ Database verification failed")
            return False
        
        print("\n🎉 Database setup completed successfully!")
        print(f"\n📋 Database Information:")
        print(f"   Host: {self.config['host']}")
        print(f"   Port: {self.config['port']}")
        print(f"   Database: {self.config['database']}")
        print(f"   Username: {self.config['username']}")
        print(f"   Password: {self.config['password']}")
        
        return True

def main():
    """Main function for standalone execution"""
    creator = DatabaseCreator()
    success = creator.setup_complete_database()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Run data generation: python data_generator.py")
        print("2. Or run complete setup: python setup_complete.py")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
