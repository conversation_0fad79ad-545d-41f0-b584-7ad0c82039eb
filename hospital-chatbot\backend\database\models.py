import uuid
from datetime import datetime, date, time
from sqlalchemy import Column, String, DateTime, Boolean, Float, Date, Time, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class Hospital(Base):
    __tablename__ = "hospitals"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    domain = Column(String(255), unique=True)
    api_key = Column(String(255), unique=True)
    subscription_plan = Column(String(50))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    users = relationship("User", back_populates="hospital")
    chat_sessions = relationship("ChatSession", back_populates="hospital")
    appointments = relationship("Appointment", back_populates="hospital")
    integration_settings = relationship("IntegrationSetting", back_populates="hospital")

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    email = Column(String(255), unique=True)
    password_hash = Column(String(255))
    role = Column(String(50))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    hospital = relationship("Hospital", back_populates="users")

class ChatSession(Base):
    __tablename__ = "chat_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    visitor_id = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    metadata = Column(JSON)
    
    # Relationships
    hospital = relationship("Hospital", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session")

class ChatMessage(Base):
    __tablename__ = "chat_messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("chat_sessions.id"))
    message = Column(Text, nullable=False)
    sender = Column(String(50))  # 'user' or 'bot'
    intent = Column(String(100))
    confidence = Column(Float)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    session = relationship("ChatSession", back_populates="messages")

class Appointment(Base):
    __tablename__ = "appointments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    patient_name = Column(String(255))
    patient_email = Column(String(255))
    patient_phone = Column(String(20))
    department = Column(String(100))
    preferred_date = Column(Date)
    preferred_time = Column(Time)
    status = Column(String(50), default='pending')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    hospital = relationship("Hospital", back_populates="appointments")

class IntegrationSetting(Base):
    __tablename__ = "integration_settings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    hms_type = Column(String(100))  # 'epic', 'cerner', 'custom', etc.
    api_endpoint = Column(String(500))
    api_credentials = Column(JSON)
    webhook_url = Column(String(500))
    settings = Column(JSON)
    is_active = Column(Boolean, default=False)
    
    # Relationships
    hospital = relationship("Hospital", back_populates="integration_settings")

# Additional model for Patient data (separate from appointments)
class Patient(Base):
    __tablename__ = "patients"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    name = Column(String(255), nullable=False)
    email = Column(String(255))
    phone = Column(String(20))
    date_of_birth = Column(Date)
    medical_record_number = Column(String(100))
    emergency_contact = Column(String(255))
    insurance_provider = Column(String(100))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    hospital = relationship("Hospital")
