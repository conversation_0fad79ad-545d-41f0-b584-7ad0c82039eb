import uuid
from datetime import datetime, date, time
from sqlalchemy import Column, String, DateTime, Boolean, Float, Date, Time, Text, ForeignKey, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base

class Hospital(Base):
    __tablename__ = "hospitals"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    domain = Column(String(255), unique=True)
    api_key = Column(String(255), unique=True)

    # SaaS subscription fields
    subscription_plan = Column(String(50), default='trial')  # trial, basic, premium, enterprise
    subscription_status = Column(String(50), default='active')  # active, suspended, cancelled
    subscription_start_date = Column(DateTime(timezone=True))
    subscription_end_date = Column(DateTime(timezone=True))
    stripe_customer_id = Column(String(255))
    stripe_subscription_id = Column(String(255))

    # Usage limits and tracking
    monthly_message_limit = Column(Integer, default=1000)
    monthly_message_count = Column(Integer, default=0)
    last_reset_date = Column(DateTime(timezone=True), server_default=func.now())

    # Hospital settings
    timezone = Column(String(50), default='UTC')
    language = Column(String(10), default='en')
    custom_branding = Column(JSON)  # Logo, colors, etc.

    # Contact and billing info
    contact_email = Column(String(255))
    billing_email = Column(String(255))
    phone = Column(String(20))
    address = Column(Text)

    # Status and metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    users = relationship("User", back_populates="hospital")
    chat_sessions = relationship("ChatSession", back_populates="hospital")
    appointments = relationship("Appointment", back_populates="hospital")
    integration_settings = relationship("IntegrationSetting", back_populates="hospital")
    subscription_history = relationship("SubscriptionHistory", back_populates="hospital")

class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    email = Column(String(255), unique=True)
    password_hash = Column(String(255))
    role = Column(String(50))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    hospital = relationship("Hospital", back_populates="users")

class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    visitor_id = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    session_metadata = Column(JSON)  # Renamed from 'metadata' to avoid SQLAlchemy conflict

    # Relationships
    hospital = relationship("Hospital", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session")

class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("chat_sessions.id"))
    message = Column(Text, nullable=False)
    sender = Column(String(50))  # 'user' or 'bot'
    intent = Column(String(100))
    confidence = Column(Float)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    session = relationship("ChatSession", back_populates="messages")

class Appointment(Base):
    __tablename__ = "appointments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    patient_name = Column(String(255))
    patient_email = Column(String(255))
    patient_phone = Column(String(20))
    department = Column(String(100))
    preferred_date = Column(Date)
    preferred_time = Column(Time)
    status = Column(String(50), default='pending')
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    hospital = relationship("Hospital", back_populates="appointments")

class IntegrationSetting(Base):
    __tablename__ = "integration_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    hms_type = Column(String(100))  # 'epic', 'cerner', 'custom', etc.
    api_endpoint = Column(String(500))
    api_credentials = Column(JSON)
    webhook_url = Column(String(500))
    settings = Column(JSON)
    is_active = Column(Boolean, default=False)

    # Relationships
    hospital = relationship("Hospital", back_populates="integration_settings")

# Additional model for Patient data (separate from appointments)
class Patient(Base):
    __tablename__ = "patients"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    name = Column(String(255), nullable=False)
    email = Column(String(255))
    phone = Column(String(20))
    date_of_birth = Column(Date)
    medical_record_number = Column(String(100))
    emergency_contact = Column(String(255))
    insurance_provider = Column(String(100))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    hospital = relationship("Hospital")

# SaaS-specific models
class SubscriptionHistory(Base):
    __tablename__ = "subscription_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    plan_name = Column(String(50), nullable=False)
    action = Column(String(50), nullable=False)  # created, upgraded, downgraded, cancelled
    amount = Column(Float)
    currency = Column(String(3), default='USD')
    stripe_invoice_id = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    hospital = relationship("Hospital", back_populates="subscription_history")

class UsageMetrics(Base):
    __tablename__ = "usage_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    metric_type = Column(String(50), nullable=False)  # messages, api_calls, storage_mb
    metric_value = Column(Integer, nullable=False)
    date = Column(Date, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    hospital = relationship("Hospital")

class AuditLog(Base):
    __tablename__ = "audit_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50))  # user, patient, appointment, etc.
    resource_id = Column(String(255))
    details = Column(JSON)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    hospital = relationship("Hospital")
    user = relationship("User")

class APIKey(Base):
    __tablename__ = "api_keys"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    key_name = Column(String(100), nullable=False)
    key_hash = Column(String(255), nullable=False)  # Hashed API key
    permissions = Column(JSON)  # List of allowed endpoints/actions
    is_active = Column(Boolean, default=True)
    last_used_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    hospital = relationship("Hospital")

class NotificationTemplate(Base):
    __tablename__ = "notification_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    hospital_id = Column(UUID(as_uuid=True), ForeignKey("hospitals.id"))
    template_type = Column(String(50), nullable=False)  # email, sms, push
    event_type = Column(String(50), nullable=False)  # appointment_confirmed, payment_due, etc.
    subject = Column(String(255))
    content = Column(Text, nullable=False)
    variables = Column(JSON)  # Available template variables
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    hospital = relationship("Hospital")

class SystemConfiguration(Base):
    __tablename__ = "system_configurations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(JSON, nullable=False)
    description = Column(Text)
    is_public = Column(Boolean, default=False)  # Can be accessed by frontend
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
