"""
Test Server for Hospital Chatbot with Simple Gemini
Minimal server to test Gemini integration without complex dependencies
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables
load_dotenv()

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Import our simple Gemini service
from services.simple_gemini_service import simple_gemini_service

# Create FastAPI app
app = FastAPI(
    title="Hospital Chatbot Test Server",
    description="Test server for Gemini integration",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatMessage(BaseModel):
    message: str
    hospital_id: str = "test_hospital"

class ChatResponse(BaseModel):
    response: str
    user_message: str
    gemini_available: bool
    model_used: str

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Hospital Chatbot Test Server",
        "gemini_available": simple_gemini_service.is_available(),
        "endpoints": ["/chat", "/health", "/test-gemini"]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "gemini_available": simple_gemini_service.is_available(),
        "model": simple_gemini_service.model_name if simple_gemini_service.is_available() else "none"
    }

@app.get("/test-gemini")
async def test_gemini():
    """Test Gemini API"""
    if not simple_gemini_service.is_available():
        return {
            "status": "error",
            "message": "Gemini not available",
            "instructions": "Configure GOOGLE_API_KEY in .env file"
        }
    
    try:
        response = simple_gemini_service.generate_response("Say 'Hello from Gemini!' and confirm you're working.")
        return {
            "status": "success",
            "test_response": response,
            "model": simple_gemini_service.model_name
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Gemini test failed: {str(e)}"
        }

@app.post("/chat", response_model=ChatResponse)
async def chat(chat_message: ChatMessage):
    """Chat endpoint"""
    
    # Sample hospital info
    hospital_info = {
        "name": "City General Hospital",
        "departments": ["Emergency", "Cardiology", "Pediatrics", "Orthopedics", "General Medicine"],
        "emergency_contact": "911",
        "phone": "(*************"
    }
    
    # Generate response
    response = simple_gemini_service.generate_response(
        chat_message.message, 
        hospital_info
    )
    
    return ChatResponse(
        response=response,
        user_message=chat_message.message,
        gemini_available=simple_gemini_service.is_available(),
        model_used=simple_gemini_service.model_name if simple_gemini_service.is_available() else "fallback"
    )

if __name__ == "__main__":
    print("🏥 Hospital Chatbot Test Server")
    print("=" * 50)
    
    # Check environment
    api_key = os.getenv('GOOGLE_API_KEY')
    if api_key and api_key != "your-gemini-api-key-here":
        print(f"✅ Gemini API key configured: {api_key[:10]}...")
    else:
        print("⚠️  Gemini API key not configured - using fallback responses")
    
    if simple_gemini_service.is_available():
        print(f"✅ Gemini service ready: {simple_gemini_service.model_name}")
    else:
        print("⚠️  Gemini service not available - using fallback responses")
    
    print("\n🚀 Starting test server...")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("🤖 Test Gemini: http://localhost:8000/test-gemini")
    print("💬 Chat Endpoint: POST to http://localhost:8000/chat")
    print()
    
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
