#!/usr/bin/env python3
"""
Database initialization script for Hospital Chatbot
"""

import sys
import os
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database import init_db, SessionLocal
from database.models import Hospital, User
from auth.auth import get_password_hash

def create_sample_data():
    """Create sample hospital and user data for testing"""
    db = SessionLocal()
    
    try:
        # Check if sample data already exists
        existing_hospital = db.query(Hospital).filter(Hospital.name == "Sample Hospital").first()
        if existing_hospital:
            print("Sample data already exists. Skipping creation.")
            return
        
        # Create sample hospital
        hospital = Hospital(
            name="Sample Hospital",
            domain="sample-hospital.com",
            api_key=str(uuid.uuid4()),
            subscription_plan="premium"
        )
        db.add(hospital)
        db.flush()  # Get the hospital ID
        
        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            password_hash=get_password_hash("admin123"),
            role="admin",
            hospital_id=hospital.id
        )
        db.add(admin_user)
        
        # Create regular user
        regular_user = User(
            email="<EMAIL>",
            password_hash=get_password_hash("user123"),
            role="user",
            hospital_id=hospital.id
        )
        db.add(regular_user)
        
        db.commit()
        
        print("Sample data created successfully!")
        print(f"Hospital: {hospital.name}")
        print(f"API Key: {hospital.api_key}")
        print(f"Admin User: <EMAIL> / admin123")
        print(f"Regular User: <EMAIL> / user123")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating sample data: {e}")
    finally:
        db.close()

def main():
    """Main function to initialize database"""
    print("Initializing Hospital Chatbot Database...")
    
    try:
        # Initialize database tables
        init_db()
        print("Database tables created successfully!")
        
        # Create sample data
        create_sample_data()
        
        print("Database initialization completed!")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
