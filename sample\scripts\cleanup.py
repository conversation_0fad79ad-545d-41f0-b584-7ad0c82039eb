#!/usr/bin/env python3
"""
Cleanup Script for Hospital Chatbot SaaS Database
Removes test data or drops entire database
"""

import sys
import os
import argparse
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from config import get_database_url, get_superuser_connection, DATABASE_CONFIG

def cleanup_data(engine):
    """Remove all data from tables"""
    print("🗑️  Removing all data from tables...")
    
    # Tables in dependency order (reverse of creation)
    tables_to_clean = [
        'chat_messages',
        'chat_sessions', 
        'appointments',
        'patients',
        'usage_metrics',
        'subscription_history',
        'audit_logs',
        'api_keys',
        'notification_templates',
        'integration_settings',
        'users',
        'hospitals',
        'system_configurations'
    ]
    
    with engine.connect() as conn:
        trans = conn.begin()
        try:
            # Disable foreign key checks temporarily
            conn.execute(text("SET session_replication_role = replica;"))
            
            total_deleted = 0
            for table in tables_to_clean:
                try:
                    result = conn.execute(text(f"DELETE FROM {table}"))
                    deleted = result.rowcount
                    total_deleted += deleted
                    print(f"   {table:25}: {deleted:6,} records deleted")
                except Exception as e:
                    print(f"   {table:25}: ❌ Error - {e}")
            
            # Re-enable foreign key checks
            conn.execute(text("SET session_replication_role = DEFAULT;"))
            
            trans.commit()
            print(f"\n✅ Total records deleted: {total_deleted:,}")
            
        except Exception as e:
            trans.rollback()
            print(f"❌ Error during cleanup: {e}")
            raise

def drop_database():
    """Drop the entire database"""
    print("💥 Dropping entire database...")
    
    try:
        # Connect as superuser to postgres database
        conn = get_superuser_connection()
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        db_name = DATABASE_CONFIG['database']
        username = DATABASE_CONFIG['username']
        
        # Terminate existing connections
        cursor.execute(f"""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
        """)
        
        # Drop database
        cursor.execute(f'DROP DATABASE IF EXISTS "{db_name}"')
        print(f"✅ Database '{db_name}' dropped")
        
        # Drop user
        cursor.execute(f"DROP USER IF EXISTS {username}")
        print(f"✅ User '{username}' dropped")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error dropping database: {e}")
        raise

def main():
    """Main cleanup function"""
    parser = argparse.ArgumentParser(description='Cleanup Hospital Chatbot SaaS Database')
    parser.add_argument('--drop-database', action='store_true', 
                       help='Drop the entire database (WARNING: This is irreversible!)')
    parser.add_argument('--confirm', action='store_true',
                       help='Confirm the cleanup action')
    
    args = parser.parse_args()
    
    print("🏥 Hospital Chatbot SaaS - Database Cleanup")
    print("=" * 50)
    
    if args.drop_database:
        print("⚠️  WARNING: This will DROP THE ENTIRE DATABASE!")
        print("   - All data will be permanently lost")
        print("   - Database and user will be removed")
        print("   - This action cannot be undone")
    else:
        print("This will remove all data from tables but keep the database structure")
    
    if not args.confirm:
        response = input("\nAre you sure you want to proceed? Type 'yes' to confirm: ")
        if response.lower() != 'yes':
            print("Cleanup cancelled.")
            sys.exit(0)
    
    try:
        if args.drop_database:
            drop_database()
            print("\n💥 Database completely removed!")
            print("To recreate, run: python setup_complete.py")
        else:
            engine = create_engine(get_database_url())
            cleanup_data(engine)
            print("\n🗑️  All data removed successfully!")
            print("To regenerate data, run: python scripts/generate_dummy.py")
            
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
