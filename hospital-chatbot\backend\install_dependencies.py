#!/usr/bin/env python3
"""
Dependency Installation Script for Hospital Chatbot SaaS
Installs packages in the correct order to avoid conflicts
"""

import subprocess
import sys
import os

def run_pip_install(packages, description=""):
    """Run pip install with error handling"""
    if description:
        print(f"\n📦 Installing {description}...")
    
    cmd = [sys.executable, "-m", "pip", "install"] + packages
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Installation successful!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def install_minimal_requirements():
    """Install minimal requirements first"""
    print("🚀 Installing Minimal Requirements for Hospital Chatbot SaaS")
    print("=" * 60)
    
    # Core packages first
    core_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "python-multipart==0.0.6",
        "python-dotenv==1.0.0",
        "requests==2.31.0",
        "click==8.1.7",
        "typing-extensions>=4.0.0"
    ]
    
    if not run_pip_install(core_packages, "Core FastAPI packages"):
        return False
    
    # Database packages
    db_packages = [
        "sqlalchemy>=1.4.0,<1.5.0",
        "psycopg2-binary==2.9.9"
    ]
    
    if not run_pip_install(db_packages, "Database packages"):
        return False
    
    # Authentication packages
    auth_packages = [
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "bcrypt==4.1.2"
    ]
    
    if not run_pip_install(auth_packages, "Authentication packages"):
        return False
    
    # Pydantic (compatible version)
    pydantic_packages = [
        "pydantic[email]>=1.10.0,<2.0.0"
    ]
    
    if not run_pip_install(pydantic_packages, "Pydantic packages"):
        return False
    
    # AI packages
    ai_packages = [
        "google-generativeai==0.3.2"
    ]
    
    if not run_pip_install(ai_packages, "AI packages"):
        return False
    
    # Date utilities
    util_packages = [
        "python-dateutil==2.8.2"
    ]
    
    if not run_pip_install(util_packages, "Utility packages"):
        return False
    
    print("\n🎉 Minimal installation completed successfully!")
    return True

def install_optional_packages():
    """Install optional packages"""
    print("\n📦 Installing Optional Packages...")
    print("=" * 40)
    
    optional_groups = {
        "SaaS Features": [
            "stripe==7.8.0",
            "redis==5.0.1",
            "boto3==1.34.0",
            "sentry-sdk[fastapi]==1.39.2"
        ],
        "Development Tools": [
            "pytest==7.4.3",
            "pytest-asyncio==0.21.1"
        ],
        "Production Server": [
            "gunicorn==21.2.0"
        ]
    }
    
    for group_name, packages in optional_groups.items():
        response = input(f"\nInstall {group_name}? (y/N): ")
        if response.lower() == 'y':
            run_pip_install(packages, group_name)
        else:
            print(f"⏭️  Skipping {group_name}")

def install_rasa():
    """Install Rasa separately due to conflicts"""
    print("\n🤖 Rasa Installation")
    print("=" * 30)
    
    response = input("Install Rasa? (This may cause conflicts) (y/N): ")
    if response.lower() != 'y':
        print("⏭️  Skipping Rasa installation")
        print("💡 You can install Rasa later in a separate environment if needed")
        return
    
    rasa_packages = [
        "rasa==3.6.13",
        "rasa-sdk==3.6.2"
    ]
    
    print("⚠️  Installing Rasa may downgrade some packages...")
    if run_pip_install(rasa_packages, "Rasa packages"):
        print("✅ Rasa installed successfully!")
        print("⚠️  Some packages may have been downgraded for compatibility")
    else:
        print("❌ Rasa installation failed")
        print("💡 Consider using a separate virtual environment for Rasa")

def main():
    """Main installation function"""
    print("🏥 Hospital Chatbot SaaS - Dependency Installation")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python version: {sys.version}")
    
    # Install minimal requirements
    if not install_minimal_requirements():
        print("\n❌ Minimal installation failed!")
        print("💡 Try installing packages manually:")
        print("   pip install fastapi uvicorn python-dotenv sqlalchemy psycopg2-binary")
        sys.exit(1)
    
    # Test basic imports
    print("\n🧪 Testing basic imports...")
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import psycopg2
        print("✅ All core packages imported successfully!")
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        sys.exit(1)
    
    # Install optional packages
    install_optional_packages()
    
    # Install Rasa (optional)
    install_rasa()
    
    print("\n🎉 Installation completed!")
    print("\n🚀 Next steps:")
    print("1. Test the installation: python main.py")
    print("2. Check API docs: http://localhost:8000/docs")
    print("3. Configure your .env file with API keys")

if __name__ == "__main__":
    main()
