import asyncio
import aiohttp
from typing import Dict, List

class RasaService:
    def __init__(self, rasa_url: str = "http://localhost:5005"):
        self.rasa_url = rasa_url
    
    async def send_message(self, message: str, sender: str, hospital_id: str) -> Dict:
        payload = {
            "sender": f"{hospital_id}_{sender}",
            "message": message
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.rasa_url}/webhooks/rest/webhook",
                json=payload
            ) as response:
                return await response.json()
    
    async def train_model(self, hospital_id: str, training_data: Dict):
        # Custom training for hospital-specific data
        pass