from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

# Import routers and database
from .routers import chat, auth, hospital, dashboard
from .database import init_db

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting Hospital Chatbot API...")
    # Initialize database tables
    init_db()
    yield
    # Shutdown
    print("Shutting down Hospital Chatbot API...")

app = FastAPI(
    title="Hospital Chatbot SaaS API",
    description="A comprehensive chatbot solution for hospitals",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hospital Chatbot API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Include routers
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(hospital.router, prefix="/api/hospital", tags=["hospital"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])