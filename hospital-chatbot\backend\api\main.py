from fastapi import FastAP<PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from .routers import chat, auth, hospital, dashboard
from .database import database
from .auth import get_current_user

app = FastAPI(title="Hospital Chatbot SaaS API")

# Include routers
app.include_router(chat.router, prefix="/api/chat")
app.include_router(auth.router, prefix="/api/auth")
app.include_router(hospital.router, prefix="/api/hospital")
app.include_router(dashboard.router, prefix="/api/dashboard")