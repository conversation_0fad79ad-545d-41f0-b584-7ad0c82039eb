from abc import ABC, abstractmethod
from typing import Dict, List, Any
import httpx

class HMSAdapter(ABC):
    """Base class for Hospital Management System integrations"""
    
    @abstractmethod
    async def authenticate(self, credentials: Dict) -> bool:
        pass
    
    @abstractmethod
    async def create_patient(self, patient_data: Dict) -> Dict:
        pass
    
    @abstractmethod
    async def book_appointment(self, appointment_data: Dict) -> Dict:
        pass
    
    @abstractmethod
    async def get_departments(self) -> List[Dict]:
        pass

class EpicAdapter(HMSAdapter):
    def __init__(self, base_url: str, client_id: str):
        self.base_url = base_url
        self.client_id = client_id
        self.access_token = None
    
    async def authenticate(self, credentials: Dict) -> bool:
        # Epic FHIR OAuth2 authentication
        auth_url = f"{self.base_url}/oauth2/token"
        async with httpx.AsyncClient() as client:
            response = await client.post(auth_url, data={
                'grant_type': 'client_credentials',
                'client_id': self.client_id,
                'client_secret': credentials['client_secret']
            })
            
            if response.status_code == 200:
                self.access_token = response.json()['access_token']
                return True
        return False
    
    async def create_patient(self, patient_data: Dict) -> Dict:
        # FHIR Patient resource creation
        fhir_patient = {
            "resourceType": "Patient",
            "name": [{
                "family": patient_data['last_name'],
                "given": [patient_data['first_name']]
            }],
            "telecom": [
                {"system": "email", "value": patient_data['email']},
                {"system": "phone", "value": patient_data['phone']}
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/Patient",
                json=fhir_patient,
                headers=headers
            )
            return response.json()

class CustomHMSAdapter(HMSAdapter):
    """For hospitals with custom HMS solutions"""
    
    def __init__(self, webhook_config: Dict):
        self.webhook_url = webhook_config['url']
        self.auth_headers = webhook_config.get('headers', {})
    
    async def create_patient(self, patient_data: Dict) -> Dict:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.webhook_url}/patients",
                json=patient_data,
                headers=self.auth_headers
            )
            return response.json()