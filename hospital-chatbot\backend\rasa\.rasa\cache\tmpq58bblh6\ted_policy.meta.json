{"hidden_layers_sizes": {"text": [], "action_text": [], "label_action_text": []}, "dense_dimension": {"text": 128, "action_text": 128, "label_action_text": 128, "intent": 20, "action_name": 20, "label_action_name": 20, "entities": 20, "slots": 20, "active_loop": 20}, "concat_dimension": {"text": 128, "action_text": 128, "label_action_text": 128}, "encoding_dimension": 50, "transformer_size": {"text": 128, "action_text": 128, "label_action_text": 128, "dialogue": 128}, "number_of_transformer_layers": {"text": 1, "action_text": 1, "label_action_text": 1, "dialogue": 1}, "number_of_attention_heads": 4, "use_key_relative_attention": false, "use_value_relative_attention": false, "max_relative_position": 5, "unidirectional_encoder": false, "batch_size": [64, 256], "batch_strategy": "balanced", "epochs": 100, "random_seed": null, "learning_rate": 0.001, "embedding_dimension": 20, "number_of_negative_examples": 20, "similarity_type": "inner", "loss_type": "cross_entropy", "ranking_length": 0, "renormalize_confidences": false, "maximum_positive_similarity": 0.8, "maximum_negative_similarity": -0.2, "use_maximum_negative_similarity": true, "scale_loss": true, "regularization_constant": 0.001, "negative_margin_scale": 0.8, "drop_rate_dialogue": 0.1, "drop_rate": 0.0, "drop_rate_label": 0.0, "drop_rate_attention": 0.0, "connection_density": 0.2, "use_sparse_input_dropout": true, "use_dense_input_dropout": true, "use_masked_language_model": false, "evaluate_every_number_of_epochs": 20, "evaluate_on_number_of_examples": 0, "tensorboard_log_directory": null, "tensorboard_log_level": "epoch", "checkpoint_model": false, "e2e_confidence_threshold": 0.5, "featurizers": [], "entity_recognition": false, "constrain_similarities": true, "model_confidence": "softmax", "BILOU_flag": true, "split_entities_by_comma": true, "max_history": 5, "priority": 1, "use_gpu": true}