/**
 * Hospital Chatbot Widget
 * Embeddable chat widget for hospital websites
 */

(function() {
    'use strict';

    // Widget configuration
    const config = window.HospitalChatbot || {};
    const apiUrl = config.apiUrl || 'http://localhost:8000/api';
    const apiKey = config.apiKey || '';
    const hospitalId = config.hospitalId || '';
    const theme = config.theme || {};

    // Default theme
    const defaultTheme = {
        primaryColor: '#007bff',
        position: 'bottom-right',
        zIndex: 9999,
        borderRadius: '10px'
    };

    const finalTheme = { ...defaultTheme, ...theme };

    // Widget state
    let isOpen = false;
    let sessionId = null;
    let visitorId = generateVisitorId();

    // Create widget HTML
    function createWidget() {
        const widgetHtml = `
            <div id="hospital-chatbot-container" style="
                position: fixed;
                ${finalTheme.position.includes('bottom') ? 'bottom: 20px;' : 'top: 20px;'}
                ${finalTheme.position.includes('right') ? 'right: 20px;' : 'left: 20px;'}
                z-index: ${finalTheme.zIndex};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <!-- Chat Button -->
                <div id="chat-button" style="
                    width: 60px;
                    height: 60px;
                    background: ${finalTheme.primaryColor};
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transition: all 0.3s ease;
                " onclick="toggleChat()">
                    <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                    </svg>
                </div>

                <!-- Chat Window -->
                <div id="chat-window" style="
                    position: absolute;
                    ${finalTheme.position.includes('bottom') ? 'bottom: 80px;' : 'top: 80px;'}
                    ${finalTheme.position.includes('right') ? 'right: 0;' : 'left: 0;'}
                    width: 350px;
                    height: 500px;
                    background: white;
                    border-radius: ${finalTheme.borderRadius};
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    display: none;
                    flex-direction: column;
                    overflow: hidden;
                ">
                    <!-- Header -->
                    <div style="
                        background: ${finalTheme.primaryColor};
                        color: white;
                        padding: 15px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    ">
                        <div style="display: flex; align-items: center;">
                            <div style="
                                width: 8px;
                                height: 8px;
                                background: #4CAF50;
                                border-radius: 50%;
                                margin-right: 8px;
                            "></div>
                            <span style="font-weight: 600;">${hospitalId} Assistant</span>
                        </div>
                        <div style="cursor: pointer; padding: 5px;" onclick="toggleChat()">
                            <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Messages -->
                    <div id="chat-messages" style="
                        flex: 1;
                        padding: 15px;
                        overflow-y: auto;
                        background: #f8f9fa;
                    ">
                        <div class="bot-message" style="margin-bottom: 15px;">
                            <div style="
                                background: white;
                                padding: 12px;
                                border-radius: 18px;
                                max-width: 80%;
                                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                            ">
                                Hello! I'm the AI assistant for ${hospitalId}. How can I help you today?
                            </div>
                            <div style="font-size: 11px; color: #666; margin-top: 5px;">Just now</div>
                        </div>
                    </div>

                    <!-- Input -->
                    <div style="
                        padding: 15px;
                        background: white;
                        border-top: 1px solid #eee;
                        display: flex;
                        gap: 10px;
                    ">
                        <input
                            type="text"
                            id="chat-input"
                            placeholder="Type your message..."
                            style="
                                flex: 1;
                                border: 1px solid #ddd;
                                border-radius: 20px;
                                padding: 10px 15px;
                                outline: none;
                                font-size: 14px;
                            "
                            onkeypress="handleKeyPress(event)"
                        >
                        <button
                            onclick="sendMessage()"
                            style="
                                background: ${finalTheme.primaryColor};
                                border: none;
                                border-radius: 50%;
                                width: 40px;
                                height: 40px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                            "
                        >
                            <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', widgetHtml);
    }

    // Toggle chat window
    window.toggleChat = function() {
        const chatWindow = document.getElementById('chat-window');
        const chatButton = document.getElementById('chat-button');

        isOpen = !isOpen;

        if (isOpen) {
            chatWindow.style.display = 'flex';
            chatButton.style.transform = 'scale(0.9)';
            document.getElementById('chat-input').focus();
        } else {
            chatWindow.style.display = 'none';
            chatButton.style.transform = 'scale(1)';
        }
    };

    // Handle enter key press
    window.handleKeyPress = function(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    };

    // Send message
    window.sendMessage = function() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();

        if (!message) return;

        // Add user message
        addMessage(message, 'user');
        input.value = '';

        // Show typing indicator
        showTypingIndicator();

        // Send to API
        fetch(`${apiUrl}/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                message: message,
                visitor_id: visitorId,
                session_id: sessionId,
                hospital_id: hospitalId
            })
        })
        .then(response => response.json())
        .then(data => {
            hideTypingIndicator();

            if (data.response) {
                addMessage(data.response, 'bot');
                sessionId = data.session_id;
            } else {
                addMessage('Sorry, I encountered an error. Please try again.', 'bot');
            }
        })
        .catch(error => {
            hideTypingIndicator();
            console.error('Chat error:', error);
            addMessage('Sorry, I\'m having trouble connecting. Please try again later.', 'bot');
        });
    };

    // Add message to chat
    function addMessage(message, sender) {
        const messagesContainer = document.getElementById('chat-messages');
        const isBot = sender === 'bot';

        const messageHtml = `
            <div class="${sender}-message" style="
                margin-bottom: 15px;
                display: flex;
                ${isBot ? 'justify-content: flex-start' : 'justify-content: flex-end'};
            ">
                <div>
                    <div style="
                        background: ${isBot ? 'white' : finalTheme.primaryColor};
                        color: ${isBot ? '#333' : 'white'};
                        padding: 12px;
                        border-radius: 18px;
                        max-width: 80%;
                        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        word-wrap: break-word;
                    ">
                        ${message}
                    </div>
                    <div style="
                        font-size: 11px;
                        color: #666;
                        margin-top: 5px;
                        text-align: ${isBot ? 'left' : 'right'};
                    ">
                        Just now
                    </div>
                </div>
            </div>
        `;

        messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Show typing indicator
    function showTypingIndicator() {
        const messagesContainer = document.getElementById('chat-messages');

        const typingHtml = `
            <div id="typing-indicator" style="margin-bottom: 15px;">
                <div style="
                    background: white;
                    padding: 12px;
                    border-radius: 18px;
                    max-width: 80%;
                    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                    display: flex;
                    align-items: center;
                    gap: 4px;
                ">
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #ccc;
                        border-radius: 50%;
                        animation: typing 1.4s infinite ease-in-out;
                    "></div>
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #ccc;
                        border-radius: 50%;
                        animation: typing 1.4s infinite ease-in-out 0.2s;
                    "></div>
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #ccc;
                        border-radius: 50%;
                        animation: typing 1.4s infinite ease-in-out 0.4s;
                    "></div>
                </div>
            </div>
        `;

        messagesContainer.insertAdjacentHTML('beforeend', typingHtml);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Add CSS animation
        if (!document.getElementById('typing-animation')) {
            const style = document.createElement('style');
            style.id = 'typing-animation';
            style.textContent = `
                @keyframes typing {
                    0%, 60%, 100% { transform: translateY(0); opacity: 0.5; }
                    30% { transform: translateY(-10px); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Hide typing indicator
    function hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    // Generate visitor ID
    function generateVisitorId() {
        let id = localStorage.getItem('hospital_chatbot_visitor_id');
        if (!id) {
            id = 'visitor_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            localStorage.setItem('hospital_chatbot_visitor_id', id);
        }
        return id;
    }

    // Initialize widget
    function init() {
        // Check if required config is provided
        if (!apiKey || !hospitalId) {
            console.error('Hospital Chatbot: Missing required configuration (apiKey, hospitalId)');
            return;
        }

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createWidget);
        } else {
            createWidget();
        }
    }

    // Start initialization
    init();

})();
