#!/usr/bin/env python3
"""
Generate Dummy Data Only Script
Generates dummy data and inserts into database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_generator import DataGenerator

def main():
    """Generate dummy data only"""
    print("📊 Generating Dummy Data")
    print("=" * 50)
    
    try:
        generator = DataGenerator()
        generator.generate_all_data()
        print("\n✅ Dummy data generated and inserted successfully!")
    except Exception as e:
        print(f"\n❌ Failed to generate dummy data: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
