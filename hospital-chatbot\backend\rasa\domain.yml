version: "3.1"

intents:
  - greet
  - goodbye
  - register_patient
  - check_symptoms
  - book_appointment
  - insurance_query
  - ask_department_info
  - ask_visiting_hours
  - emergency_contact

entities:
  - patient_name
  - patient_email
  - patient_phone
  - symptom
  - department
  - insurance_provider
  - appointment_date
  - appointment_time

slots:
  patient_name:
    type: text
    mappings:
    - type: from_entity
      entity: patient_name

  patient_email:
    type: text
    mappings:
    - type: from_entity
      entity: patient_email

  symptoms:
    type: list
    mappings:
    - type: from_entity
      entity: symptom

forms:
  patient_registration_form:
    required_slots:
      - patient_name
      - patient_email
      - patient_phone

  appointment_booking_form:
    required_slots:
      - patient_name
      - patient_email
      - department
      - appointment_date
      - appointment_time

responses:
  utter_greet:
  - text: "Hello! I'm your virtual assistant. How can I help you today?"
  - text: "Hi there! Welcome to our hospital. What can I assist you with?"
  - text: "Good day! I'm here to help with your hospital needs. How may I assist you?"

  utter_goodbye:
  - text: "Thank you for contacting us. Have a great day!"
  - text: "Goodbye! Feel free to reach out if you need any assistance."

  utter_ask_patient_name:
  - text: "What's your full name?"
  - text: "Could you please provide your full name?"

  utter_ask_patient_email:
  - text: "What's your email address?"
  - text: "Please provide your email address for contact purposes."

  utter_ask_patient_phone:
  - text: "What's your phone number?"
  - text: "Could you share your contact phone number?"

  utter_ask_symptoms:
  - text: "Can you describe your symptoms?"
  - text: "Please tell me about the symptoms you're experiencing."

  utter_ask_department:
  - text: "Which department would you like to book an appointment with?"
  - text: "What medical specialty or department do you need?"

  utter_ask_appointment_date:
  - text: "What date would you prefer for your appointment?"
  - text: "When would you like to schedule your appointment?"

  utter_ask_appointment_time:
  - text: "What time would work best for you?"
  - text: "Please let me know your preferred time."

  utter_department_info:
  - text: "We have several departments including Cardiology, Pediatrics, Orthopedics, Emergency Medicine, and General Medicine. Which one interests you?"

  utter_visiting_hours:
  - text: "Our visiting hours are Monday-Friday 9 AM to 8 PM, and weekends 10 AM to 6 PM."

  utter_emergency_contact:
  - text: "For emergencies, please call 911 or visit our Emergency Department immediately. Our emergency department is open 24/7."

  utter_insurance_info:
  - text: "We accept most major insurance plans. Please provide your insurance details and we'll verify coverage for you."

  utter_appointment_confirmation:
  - text: "Great! I'm processing your appointment request. You'll receive a confirmation email shortly."

  utter_registration_complete:
  - text: "Your registration is complete! Welcome to our hospital family."

  utter_default:
  - text: "I'm sorry, I didn't understand that. Could you please rephrase your question?"
  - text: "I'm not sure I understand. Can you tell me more about what you need help with?"

actions:
  - action_register_patient
  - action_book_appointment
  - action_check_symptoms
  - action_insurance_lookup

# session_config:
#   session_expiration_time: 60
#   carry_over_slots_to_new_session: true
