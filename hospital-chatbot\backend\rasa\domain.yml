version: "3.1"

intents:
  - greet
  - goodbye
  - register_patient
  - check_symptoms
  - book_appointment
  - insurance_query
  - ask_department_info
  - ask_visiting_hours
  - emergency_contact

entities:
  - patient_name
  - patient_email
  - patient_phone
  - symptom
  - department
  - insurance_provider
  - appointment_date
  - appointment_time

slots:
  patient_name:
    type: text
    mappings:
    - type: from_entity
      entity: patient_name
  
  patient_email:
    type: text
    mappings:
    - type: from_entity
      entity: patient_email
  
  symptoms:
    type: list
    mappings:
    - type: from_entity
      entity: symptom

forms:
  patient_registration_form:
    required_slots:
      - patient_name
      - patient_email
      - patient_phone
  
  appointment_booking_form:
    required_slots:
      - patient_name
      - patient_email
      - department
      - appointment_date
      - appointment_time

responses:
  utter_greet:
  - text: "Hello! I'm your virtual assistant. How can I help you today?"
  
  utter_ask_patient_name:
  - text: "What's your full name?"
  
  utter_ask_symptoms:
  - text: "Can you describe your symptoms?"

actions:
  - action_register_patient
  - action_book_appointment
  - action_check_symptoms
  - action_insurance_lookup

# session_config:
#   session_expiration_time: 60
#   carry_over_slots_to_new_session: true
