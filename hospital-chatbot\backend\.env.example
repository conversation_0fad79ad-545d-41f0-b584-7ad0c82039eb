# Database Configuration
DATABASE_URL=sqlite:///./hospital_chatbot.db
# For PostgreSQL: postgresql://username:password@localhost/hospital_chatbot

# Security
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rasa Configuration
RASA_URL=http://localhost:5005
RASA_ACTION_ENDPOINT=http://localhost:5055/webhook

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Hospital Management System Integrations
# Epic FHIR
EPIC_BASE_URL=https://fhir.epic.com/interconnect-fhir-oauth
EPIC_CLIENT_ID=your-epic-client-id

# Cerner FHIR
CERNER_BASE_URL=https://fhir-open.cerner.com
CERNER_CLIENT_ID=your-cerner-client-id

# Logging
LOG_LEVEL=INFO
LOG_FILE=hospital_chatbot.log

# Redis (for session storage and caching)
REDIS_URL=redis://localhost:6379/0

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn-for-error-tracking
