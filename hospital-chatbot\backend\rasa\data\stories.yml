version: "3.1"

stories:

- story: greeting and appointment booking
  steps:
  - intent: greet
  - action: utter_greet
  - intent: book_appointment
  - action: appointment_booking_form
  - active_loop: appointment_booking_form
  - active_loop: null
  - action: action_book_appointment

- story: patient registration flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: register_patient
  - action: patient_registration_form
  - active_loop: patient_registration_form
  - active_loop: null
  - action: action_register_patient

- story: symptom checking
  steps:
  - intent: greet
  - action: utter_greet
  - intent: check_symptoms
  - action: utter_ask_symptoms
  - action: action_check_symptoms

- story: department information inquiry
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_department_info
  - action: utter_department_info

- story: visiting hours inquiry
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_visiting_hours
  - action: utter_visiting_hours

- story: emergency contact request
  steps:
  - intent: emergency_contact
  - action: utter_emergency_contact

- story: insurance inquiry
  steps:
  - intent: greet
  - action: utter_greet
  - intent: insurance_query
  - action: utter_insurance_info

- story: direct appointment booking
  steps:
  - intent: book_appointment
  - action: appointment_booking_form
  - active_loop: appointment_booking_form
  - active_loop: null
  - action: action_book_appointment

- story: goodbye flow
  steps:
  - intent: goodbye
  - action: utter_goodbye

- story: bot challenge
  steps:
  - intent: bot_challenge
  - action: utter_iamabot
