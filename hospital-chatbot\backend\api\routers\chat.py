from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uuid
import logging
from datetime import datetime, timezone

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database import get_db
from database.models import Hospital, ChatSession, ChatMessage, UsageMetrics
from auth import verify_api_key
from services.rasa_service import RasaService
from services.gemini_service import gemini_service

router = APIRouter()

# Initialize Rasa service
rasa_service = RasaService()

# Pydantic models
class ChatMessageRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    visitor_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ChatMessageResponse(BaseModel):
    response: str
    intent: Optional[str] = None
    confidence: Optional[float] = None
    session_id: str
    suggestions: Optional[List[str]] = None

class ChatHistoryResponse(BaseModel):
    id: str
    message: str
    sender: str
    intent: Optional[str] = None
    confidence: Optional[float] = None
    created_at: str

    class Config:
        from_attributes = True

class SessionResponse(BaseModel):
    id: str
    visitor_id: str
    created_at: str
    ended_at: Optional[str] = None
    message_count: int

@router.post("/message", response_model=ChatMessageResponse)
async def send_message(
    request: ChatMessageRequest,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Send a message to the chatbot and get response"""

    # Get or create chat session
    session = None
    if request.session_id:
        session = db.query(ChatSession).filter(
            ChatSession.id == request.session_id,
            ChatSession.hospital_id == hospital.id
        ).first()

    if not session:
        # Create new session
        session = ChatSession(
            hospital_id=hospital.id,
            visitor_id=request.visitor_id or str(uuid.uuid4()),
            session_metadata=request.metadata or {}
        )
        db.add(session)
        db.flush()

    # Save user message
    user_message = ChatMessage(
        session_id=session.id,
        message=request.message,
        sender="user"
    )
    db.add(user_message)

    try:
        # Check usage limits for SaaS
        if not await check_usage_limits(hospital, db):
            return ChatMessageResponse(
                response="You have reached your monthly message limit. Please upgrade your plan to continue.",
                intent="usage_limit_exceeded",
                confidence=1.0,
                session_id=str(session.id),
                suggestions=["Upgrade plan", "Contact support"]
            )

        # Get conversation context
        context = await get_conversation_context(session.id, db)

        # Prepare hospital information for AI
        hospital_info = {
            "name": hospital.name,
            "departments": ["Emergency", "Cardiology", "Pediatrics", "General Medicine"],  # This should come from hospital settings
            "emergency_contact": "911",
            "visiting_hours": "9 AM - 8 PM (Mon-Fri), 10 AM - 6 PM (Weekends)"
        }

        # Try Rasa first for intent detection
        rasa_response = None
        intent = None
        confidence = 0.0

        try:
            rasa_response = await rasa_service.send_message(
                message=request.message,
                sender=session.visitor_id,
                hospital_id=str(hospital.id)
            )

            if rasa_response and len(rasa_response) > 0:
                intent = rasa_response[0].get("intent", {}).get("name") if "intent" in rasa_response[0] else None
                confidence = rasa_response[0].get("intent", {}).get("confidence", 0.0) if "intent" in rasa_response[0] else 0.0
        except Exception as e:
            logger.warning(f"Rasa service unavailable, falling back to Gemini: {e}")

        # Use Gemini AI for response generation
        ai_response = await gemini_service.generate_response(
            message=request.message,
            context=context,
            intent=intent,
            hospital_info=hospital_info
        )

        bot_response = ai_response.get("response", "I'm sorry, I'm having trouble processing your request right now.")
        intent = intent or ai_response.get("intent", "general")
        confidence = max(confidence, ai_response.get("confidence", 0.0))
        suggestions = ai_response.get("suggestions", [])

        # Save bot response
        bot_message = ChatMessage(
            session_id=session.id,
            message=bot_response,
            sender="bot",
            intent=intent,
            confidence=confidence
        )
        db.add(bot_message)
        db.commit()

        # Generate suggestions based on intent
        suggestions = generate_suggestions(intent, hospital.name)

        return ChatMessageResponse(
            response=bot_response,
            intent=intent,
            confidence=confidence,
            session_id=str(session.id),
            suggestions=suggestions
        )

    except Exception as e:
        db.rollback()
        # Fallback response
        fallback_response = "I'm sorry, I'm experiencing technical difficulties. Please try again later."

        bot_message = ChatMessage(
            session_id=session.id,
            message=fallback_response,
            sender="bot",
            intent="fallback",
            confidence=0.0
        )
        db.add(bot_message)
        db.commit()

        return ChatMessageResponse(
            response=fallback_response,
            intent="fallback",
            confidence=0.0,
            session_id=str(session.id)
        )

@router.get("/sessions", response_model=List[SessionResponse])
async def get_chat_sessions(
    limit: int = 50,
    offset: int = 0,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Get chat sessions for the hospital"""
    sessions = db.query(ChatSession).filter(
        ChatSession.hospital_id == hospital.id
    ).order_by(ChatSession.created_at.desc()).offset(offset).limit(limit).all()

    result = []
    for session in sessions:
        message_count = db.query(ChatMessage).filter(
            ChatMessage.session_id == session.id
        ).count()

        result.append(SessionResponse(
            id=str(session.id),
            visitor_id=session.visitor_id,
            created_at=session.created_at.isoformat(),
            ended_at=session.ended_at.isoformat() if session.ended_at else None,
            message_count=message_count
        ))

    return result

@router.get("/sessions/{session_id}/messages", response_model=List[ChatHistoryResponse])
async def get_session_messages(
    session_id: str,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Get messages for a specific chat session"""

    # Verify session belongs to hospital
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.hospital_id == hospital.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at.asc()).all()

    return [
        ChatHistoryResponse(
            id=str(msg.id),
            message=msg.message,
            sender=msg.sender,
            intent=msg.intent,
            confidence=msg.confidence,
            created_at=msg.created_at.isoformat()
        )
        for msg in messages
    ]

@router.post("/sessions/{session_id}/end")
async def end_chat_session(
    session_id: str,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """End a chat session"""

    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.hospital_id == hospital.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    session.ended_at = datetime.now(timezone.utc)
    db.commit()

    return {"message": "Session ended successfully"}

def generate_suggestions(intent: Optional[str], hospital_name: str) -> List[str]:
    """Generate contextual suggestions based on intent"""
    suggestions_map = {
        "greet": [
            "I'd like to book an appointment",
            "What are your visiting hours?",
            "Tell me about your departments"
        ],
        "book_appointment": [
            "What departments do you have?",
            "What are your available time slots?",
            "Do you accept my insurance?"
        ],
        "check_symptoms": [
            "Book an appointment",
            "Find emergency contact",
            "Get department information"
        ],
        "ask_department_info": [
            "Book an appointment with this department",
            "What are the visiting hours?",
            "How do I contact this department?"
        ]
    }

    return suggestions_map.get(intent, [
        f"Tell me about {hospital_name}",
        "Book an appointment",
        "Contact information"
    ])

# Helper functions for SaaS features
logger = logging.getLogger(__name__)

async def check_usage_limits(hospital: Hospital, db: Session) -> bool:
    """Check if hospital has exceeded usage limits"""

    # Check if subscription is active
    if hospital.subscription_status != 'active':
        return False

    # Check message limits
    current_month = datetime.now(timezone.utc).replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    if hospital.last_reset_date < current_month:
        # Reset monthly counter
        hospital.monthly_message_count = 0
        hospital.last_reset_date = current_month
        db.commit()

    # Check if under limit
    if hospital.monthly_message_count >= hospital.monthly_message_limit:
        return False

    # Increment counter
    hospital.monthly_message_count += 1
    db.commit()

    # Log usage metrics
    today = datetime.now(timezone.utc).date()
    usage_metric = db.query(UsageMetrics).filter(
        UsageMetrics.hospital_id == hospital.id,
        UsageMetrics.metric_type == 'messages',
        UsageMetrics.date == today
    ).first()

    if usage_metric:
        usage_metric.metric_value += 1
    else:
        usage_metric = UsageMetrics(
            hospital_id=hospital.id,
            metric_type='messages',
            metric_value=1,
            date=today
        )
        db.add(usage_metric)

    db.commit()
    return True

async def get_conversation_context(session_id: str, db: Session) -> Dict[str, Any]:
    """Get conversation context for AI"""

    # Get recent messages from this session
    recent_messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at.desc()).limit(10).all()

    # Format messages for context
    previous_messages = []
    for msg in reversed(recent_messages):  # Reverse to get chronological order
        previous_messages.append({
            "sender": msg.sender,
            "message": msg.message,
            "intent": msg.intent,
            "timestamp": msg.created_at.isoformat()
        })

    return {
        "previous_messages": previous_messages,
        "session_id": session_id
    }
