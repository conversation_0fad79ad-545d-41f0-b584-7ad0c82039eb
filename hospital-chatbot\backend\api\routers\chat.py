from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime

from ...database import get_db
from ...database.models import Hospital, ChatSession, ChatMessage
from ...auth import verify_api_key
from ...services.rasa_service import RasaService

router = APIRouter()

# Initialize Rasa service
rasa_service = RasaService()

# Pydantic models
class ChatMessageRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    visitor_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ChatMessageResponse(BaseModel):
    response: str
    intent: Optional[str] = None
    confidence: Optional[float] = None
    session_id: str
    suggestions: Optional[List[str]] = None

class ChatHistoryResponse(BaseModel):
    id: str
    message: str
    sender: str
    intent: Optional[str] = None
    confidence: Optional[float] = None
    created_at: str

    class Config:
        from_attributes = True

class SessionResponse(BaseModel):
    id: str
    visitor_id: str
    created_at: str
    ended_at: Optional[str] = None
    message_count: int

@router.post("/message", response_model=ChatMessageResponse)
async def send_message(
    request: ChatMessageRequest,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Send a message to the chatbot and get response"""
    
    # Get or create chat session
    session = None
    if request.session_id:
        session = db.query(ChatSession).filter(
            ChatSession.id == request.session_id,
            ChatSession.hospital_id == hospital.id
        ).first()
    
    if not session:
        # Create new session
        session = ChatSession(
            hospital_id=hospital.id,
            visitor_id=request.visitor_id or str(uuid.uuid4()),
            metadata=request.metadata or {}
        )
        db.add(session)
        db.flush()
    
    # Save user message
    user_message = ChatMessage(
        session_id=session.id,
        message=request.message,
        sender="user"
    )
    db.add(user_message)
    
    try:
        # Send message to Rasa
        rasa_response = await rasa_service.send_message(
            message=request.message,
            sender=session.visitor_id,
            hospital_id=str(hospital.id)
        )
        
        # Extract response data
        if rasa_response and len(rasa_response) > 0:
            bot_response = rasa_response[0].get("text", "I'm sorry, I didn't understand that.")
            intent = rasa_response[0].get("intent", {}).get("name") if "intent" in rasa_response[0] else None
            confidence = rasa_response[0].get("intent", {}).get("confidence") if "intent" in rasa_response[0] else None
        else:
            bot_response = "I'm sorry, I'm having trouble processing your request right now."
            intent = None
            confidence = None
        
        # Save bot response
        bot_message = ChatMessage(
            session_id=session.id,
            message=bot_response,
            sender="bot",
            intent=intent,
            confidence=confidence
        )
        db.add(bot_message)
        db.commit()
        
        # Generate suggestions based on intent
        suggestions = generate_suggestions(intent, hospital.name)
        
        return ChatMessageResponse(
            response=bot_response,
            intent=intent,
            confidence=confidence,
            session_id=str(session.id),
            suggestions=suggestions
        )
        
    except Exception as e:
        db.rollback()
        # Fallback response
        fallback_response = "I'm sorry, I'm experiencing technical difficulties. Please try again later."
        
        bot_message = ChatMessage(
            session_id=session.id,
            message=fallback_response,
            sender="bot",
            intent="fallback",
            confidence=0.0
        )
        db.add(bot_message)
        db.commit()
        
        return ChatMessageResponse(
            response=fallback_response,
            intent="fallback",
            confidence=0.0,
            session_id=str(session.id)
        )

@router.get("/sessions", response_model=List[SessionResponse])
async def get_chat_sessions(
    limit: int = 50,
    offset: int = 0,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Get chat sessions for the hospital"""
    sessions = db.query(ChatSession).filter(
        ChatSession.hospital_id == hospital.id
    ).order_by(ChatSession.created_at.desc()).offset(offset).limit(limit).all()
    
    result = []
    for session in sessions:
        message_count = db.query(ChatMessage).filter(
            ChatMessage.session_id == session.id
        ).count()
        
        result.append(SessionResponse(
            id=str(session.id),
            visitor_id=session.visitor_id,
            created_at=session.created_at.isoformat(),
            ended_at=session.ended_at.isoformat() if session.ended_at else None,
            message_count=message_count
        ))
    
    return result

@router.get("/sessions/{session_id}/messages", response_model=List[ChatHistoryResponse])
async def get_session_messages(
    session_id: str,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Get messages for a specific chat session"""
    
    # Verify session belongs to hospital
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.hospital_id == hospital.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at.asc()).all()
    
    return [
        ChatHistoryResponse(
            id=str(msg.id),
            message=msg.message,
            sender=msg.sender,
            intent=msg.intent,
            confidence=msg.confidence,
            created_at=msg.created_at.isoformat()
        )
        for msg in messages
    ]

@router.post("/sessions/{session_id}/end")
async def end_chat_session(
    session_id: str,
    hospital: Hospital = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """End a chat session"""
    
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.hospital_id == hospital.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    session.ended_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Session ended successfully"}

def generate_suggestions(intent: Optional[str], hospital_name: str) -> List[str]:
    """Generate contextual suggestions based on intent"""
    suggestions_map = {
        "greet": [
            "I'd like to book an appointment",
            "What are your visiting hours?",
            "Tell me about your departments"
        ],
        "book_appointment": [
            "What departments do you have?",
            "What are your available time slots?",
            "Do you accept my insurance?"
        ],
        "check_symptoms": [
            "Book an appointment",
            "Find emergency contact",
            "Get department information"
        ],
        "ask_department_info": [
            "Book an appointment with this department",
            "What are the visiting hours?",
            "How do I contact this department?"
        ]
    }
    
    return suggestions_map.get(intent, [
        f"Tell me about {hospital_name}",
        "Book an appointment",
        "Contact information"
    ])
