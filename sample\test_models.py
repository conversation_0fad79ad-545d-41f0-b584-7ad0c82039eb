#!/usr/bin/env python3
"""
Test Models Script
Quick test to verify SQLAlchemy models work correctly
"""

def test_models_import():
    """Test if models can be imported without errors"""
    print("🧪 Testing model imports...")
    
    try:
        from models import (
            Base, Hospital, User, ChatSession, ChatMessage,
            Appointment, Patient, SubscriptionHistory, UsageMetrics
        )
        print("✅ All models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        return False

def test_table_creation():
    """Test if tables can be created"""
    print("🏗️  Testing table creation...")
    
    try:
        from sqlalchemy import create_engine
        from models import Base
        
        # Use in-memory SQLite for testing
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(bind=engine)
        
        # Check created tables
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"✅ Created {len(tables)} tables successfully:")
        for table in sorted(tables):
            print(f"   - {table}")
        
        return True
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🏥 Hospital Chatbot SaaS - Model Testing")
    print("=" * 50)
    
    # Test model imports
    import_ok = test_models_import()
    
    if import_ok:
        # Test table creation
        table_ok = test_table_creation()
        
        if table_ok:
            print("\n🎉 All model tests passed!")
            print("✅ Models are ready for database creation")
        else:
            print("\n❌ Table creation test failed")
    else:
        print("\n❌ Model import test failed")

if __name__ == "__main__":
    main()
