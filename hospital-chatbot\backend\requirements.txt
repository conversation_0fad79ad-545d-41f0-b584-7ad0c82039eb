# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database - Compatible versions for Rasa
sqlalchemy>=1.4.0,<1.5.0
psycopg2-binary==2.9.9
alembic>=1.8.0,<1.13.0

# Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# HTTP client for integrations
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0
pydantic[email]>=1.10.0,<3.0.0

# AI and LLM Integration (without conflicting packages)
google-generativeai==0.3.2

# Rasa integration (core packages only)
rasa==3.6.13
rasa-sdk==3.6.2

# Additional utilities
python-dateutil==2.8.2

# SaaS specific dependencies
stripe==7.8.0
redis==5.0.1
boto3==1.34.0
sentry-sdk[fastapi]==1.39.2

# Security and password hashing
bcrypt==4.1.2

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Production server
gunicorn==21.2.0

# Additional required packages
click==8.1.7
typing-extensions>=4.0.0
