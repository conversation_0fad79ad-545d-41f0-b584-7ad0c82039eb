#!/usr/bin/env python3
"""
Test Database Connection Script
Simple script to test if database connection works
"""

from config import get_db_connection, get_database_url
from sqlalchemy import create_engine

def test_psycopg2_connection():
    """Test direct psycopg2 connection"""
    print("🔌 Testing psycopg2 connection...")
    
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(f"✅ psycopg2 connection successful")
            print(f"   PostgreSQL version: {version}")
            cursor.close()
            conn.close()
            return True
        else:
            print("❌ psycopg2 connection failed")
            return False
    except Exception as e:
        print(f"❌ psycopg2 connection error: {e}")
        return False

def test_sqlalchemy_connection():
    """Test SQLAlchemy connection"""
    print("\n🔌 Testing SQLAlchemy connection...")
    
    try:
        engine = create_engine(get_database_url())
        with engine.connect() as conn:
            result = conn.execute("SELECT current_database(), current_user;")
            db_name, user = result.fetchone()
            print(f"✅ SQLAlchemy connection successful")
            print(f"   Database: {db_name}")
            print(f"   User: {user}")
            return True
    except Exception as e:
        print(f"❌ SQLAlchemy connection error: {e}")
        return False

def test_tables_exist():
    """Test if tables exist"""
    print("\n📋 Checking if tables exist...")
    
    try:
        engine = create_engine(get_database_url())
        inspector = engine.inspect(engine)
        tables = inspector.get_table_names()
        
        if tables:
            print(f"✅ Found {len(tables)} tables:")
            for table in sorted(tables):
                print(f"   - {table}")
            return True
        else:
            print("⚠️  No tables found - database may not be initialized")
            return False
    except Exception as e:
        print(f"❌ Error checking tables: {e}")
        return False

def main():
    """Main test function"""
    print("🏥 Hospital Chatbot SaaS - Database Connection Test")
    print("=" * 60)
    
    # Test connections
    psycopg2_ok = test_psycopg2_connection()
    sqlalchemy_ok = test_sqlalchemy_connection()
    
    if psycopg2_ok and sqlalchemy_ok:
        tables_ok = test_tables_exist()
        
        print("\n" + "=" * 60)
        if tables_ok:
            print("🎉 All tests passed! Database is ready to use.")
        else:
            print("⚠️  Connection works but no tables found.")
            print("   Run: python setup_complete.py")
    else:
        print("\n" + "=" * 60)
        print("❌ Connection tests failed!")
        print("\n💡 Troubleshooting tips:")
        print("   1. Ensure PostgreSQL is running")
        print("   2. Check database credentials in config.py")
        print("   3. Run: python setup_complete.py")

if __name__ == "__main__":
    main()
