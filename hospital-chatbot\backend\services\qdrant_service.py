"""
Qdrant Vector Database Service for Hospital-Specific Knowledge
Manages hospital-specific documents and provides semantic search
"""

import os
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from sentence_transformers import SentenceTransformer
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    print("⚠️  Qdrant client or sentence-transformers not installed")

class QdrantService:
    def __init__(self):
        """Initialize Qdrant service"""
        self.client = None
        self.encoder = None
        self.collection_name = "hospital_knowledge"
        
        if not QDRANT_AVAILABLE:
            print("❌ Qdrant dependencies not available")
            return
        
        try:
            # Initialize Qdrant client
            qdrant_url = os.getenv("QDRANT_URL", "http://localhost:6333")
            self.client = QdrantClient(url=qdrant_url)
            
            # Initialize sentence transformer for embeddings
            self.encoder = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Create collection if it doesn't exist
            self._create_collection()
            
            print(f"✅ Qdrant service initialized: {qdrant_url}")
            
        except Exception as e:
            print(f"❌ Failed to initialize Qdrant: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Qdrant service is available"""
        return self.client is not None and QDRANT_AVAILABLE
    
    def _create_collection(self):
        """Create collection for hospital knowledge"""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # Create collection with vector configuration
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=384,  # all-MiniLM-L6-v2 embedding size
                        distance=Distance.COSINE
                    )
                )
                print(f"✅ Created collection: {self.collection_name}")
            else:
                print(f"✅ Collection already exists: {self.collection_name}")
                
        except Exception as e:
            print(f"❌ Failed to create collection: {e}")
    
    def add_hospital_documents(self, hospital_id: str, documents: List[str], metadata: List[Dict] = None):
        """Add hospital-specific documents to vector database"""
        if not self.is_available():
            print("⚠️  Qdrant not available")
            return False
        
        try:
            points = []
            
            for i, doc in enumerate(documents):
                # Generate embedding
                embedding = self.encoder.encode(doc).tolist()
                
                # Prepare metadata
                doc_metadata = {
                    "hospital_id": hospital_id,
                    "document_index": i,
                    "text": doc[:500] + "..." if len(doc) > 500 else doc  # Truncate for storage
                }
                
                if metadata and i < len(metadata):
                    doc_metadata.update(metadata[i])
                
                # Create point
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding,
                    payload=doc_metadata
                )
                points.append(point)
            
            # Upload points to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            print(f"✅ Added {len(documents)} documents for hospital {hospital_id}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to add documents: {e}")
            return False
    
    def search_hospital_knowledge(self, hospital_id: str, query: str, limit: int = 5) -> List[Dict]:
        """Search hospital-specific knowledge"""
        if not self.is_available():
            return []
        
        try:
            # Generate query embedding
            query_embedding = self.encoder.encode(query).tolist()
            
            # Search in Qdrant
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter={
                    "must": [
                        {"key": "hospital_id", "match": {"value": hospital_id}}
                    ]
                },
                limit=limit
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    "text": result.payload.get("text", ""),
                    "score": result.score,
                    "metadata": result.payload
                })
            
            return results
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
    
    def load_hospital_documents_from_files(self, hospital_id: str, docs_directory: str):
        """Load hospital documents from text files"""
        docs_path = Path(docs_directory)
        
        if not docs_path.exists():
            print(f"❌ Documents directory not found: {docs_directory}")
            return False
        
        # Find hospital-specific document file
        hospital_files = [
            f for f in docs_path.glob("*.txt") 
            if hospital_id.lower().replace(" ", "_") in f.name.lower()
        ]
        
        if not hospital_files:
            print(f"⚠️  No document files found for hospital: {hospital_id}")
            return False
        
        all_documents = []
        all_metadata = []
        
        for file_path in hospital_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Split content into chunks (paragraphs or sections)
                chunks = self._split_document(content)
                
                for i, chunk in enumerate(chunks):
                    all_documents.append(chunk)
                    all_metadata.append({
                        "source_file": file_path.name,
                        "chunk_index": i,
                        "file_path": str(file_path)
                    })
                
                print(f"✅ Loaded {len(chunks)} chunks from {file_path.name}")
                
            except Exception as e:
                print(f"❌ Failed to load {file_path}: {e}")
        
        if all_documents:
            return self.add_hospital_documents(hospital_id, all_documents, all_metadata)
        
        return False
    
    def _split_document(self, content: str, max_chunk_size: int = 1000) -> List[str]:
        """Split document into smaller chunks for better search"""
        # Split by double newlines (paragraphs)
        paragraphs = content.split('\n\n')
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # If adding this paragraph would exceed max size, save current chunk
            if len(current_chunk) + len(paragraph) > max_chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def get_hospital_stats(self, hospital_id: str) -> Dict:
        """Get statistics for hospital documents"""
        if not self.is_available():
            return {"error": "Qdrant not available"}
        
        try:
            # Count documents for this hospital
            search_results = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter={
                    "must": [
                        {"key": "hospital_id", "match": {"value": hospital_id}}
                    ]
                },
                limit=1000  # Adjust based on expected document count
            )
            
            return {
                "hospital_id": hospital_id,
                "document_count": len(search_results[0]),
                "collection_name": self.collection_name
            }
            
        except Exception as e:
            return {"error": str(e)}

# Global instance
qdrant_service = QdrantService()
