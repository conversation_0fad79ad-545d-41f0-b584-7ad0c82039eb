#!/usr/bin/env python3
"""
Create Tables Only <PERSON>ript
Creates all database tables without data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database_creator import DatabaseCreator

def main():
    """Create tables only"""
    print("🏗️  Creating Database Tables")
    print("=" * 50)
    
    creator = DatabaseCreator()
    
    if creator.create_tables():
        print("\n✅ Tables created successfully!")
        print("\nNext steps:")
        print("1. Generate data: python scripts/generate_dummy.py")
        print("2. Insert data: python scripts/insert_data.py")
    else:
        print("\n❌ Failed to create tables")
        sys.exit(1)

if __name__ == "__main__":
    main()
