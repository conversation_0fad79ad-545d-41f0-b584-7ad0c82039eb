"""
Dummy Data Generator for Hospital Chatbot SaaS
Generates realistic dummy data for testing and development
"""

import uuid
import random
import bcrypt
from datetime import datetime, timedelta, date, time
from faker import Faker
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from tqdm import tqdm

from config import (
    get_database_url, DATA_CONFIG, HOSPITAL_TYPES,
    SUBSCRIPTION_PLANS, CHAT_INTENTS
)
from models import (
    Hospital, User, Patient, Appointment, ChatSession,
    ChatMessage, SubscriptionHistory, UsageMetrics,
    IntegrationSetting, SystemConfiguration
)

class DataGenerator:
    def __init__(self):
        self.fake = Faker()
        self.engine = create_engine(get_database_url())
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.db = SessionLocal()

        # Data storage
        self.hospitals = []
        self.users = []
        self.patients = []

    def generate_phone_number(self):
        """Generate a phone number that fits in 20 characters"""
        # Generate a simple format: +1-XXX-XXX-XXXX (14 characters)
        area_code = random.randint(200, 999)
        exchange = random.randint(200, 999)
        number = random.randint(1000, 9999)
        return f"+1-{area_code}-{exchange}-{number}"

    def truncate_string(self, text, max_length):
        """Safely truncate string to max length"""
        if text and len(text) > max_length:
            return text[:max_length-3] + "..."
        return text

    def generate_hospitals(self):
        """Generate hospital data"""
        print("🏥 Generating hospitals...")

        for i, hospital_config in enumerate(HOSPITAL_TYPES):
            plan_config = SUBSCRIPTION_PLANS[hospital_config['subscription_plan']]

            # Calculate subscription dates
            start_date = datetime.now() - timedelta(days=random.randint(30, 365))
            end_date = start_date + timedelta(days=plan_config['duration_days'])

            # Generate usage data
            usage_percentage = random.uniform(0.1, 0.9)
            message_count = int(plan_config['message_limit'] * usage_percentage)

            hospital = Hospital(
                name=hospital_config['name'],
                domain=hospital_config['domain'],
                api_key=str(uuid.uuid4()),
                subscription_plan=hospital_config['subscription_plan'],
                subscription_status='active',
                subscription_start_date=start_date,
                subscription_end_date=end_date,
                monthly_message_limit=plan_config['message_limit'],
                monthly_message_count=message_count,
                last_reset_date=datetime.now().replace(day=1),
                contact_email=f"contact@{hospital_config['domain']}",
                billing_email=f"billing@{hospital_config['domain']}",
                phone=self.generate_phone_number(),
                address=self.fake.address()[:200],  # Limit address length
                timezone=random.choice(['America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles']),
                language='en',
                custom_branding={
                    "primary_color": self.fake.hex_color(),
                    "secondary_color": self.fake.hex_color(),
                    "logo_url": None,
                    "welcome_message": f"Welcome to {hospital_config['name']}! How can I help you today?"
                },
                stripe_customer_id=f"cus_{self.fake.lexify(text='??????????')}",
                stripe_subscription_id=f"sub_{self.fake.lexify(text='??????????')}" if hospital_config['subscription_plan'] != 'trial' else None
            )

            self.db.add(hospital)
            self.hospitals.append(hospital)

        self.db.flush()  # Get IDs
        print(f"✅ Generated {len(self.hospitals)} hospitals")

    def generate_users(self):
        """Generate user data"""
        print("👥 Generating users...")

        password_hash = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        for hospital in tqdm(self.hospitals):
            # Create admin user
            admin_user = User(
                hospital_id=hospital.id,
                email=f"admin@{hospital.domain}",
                password_hash=password_hash,
                role="admin"
            )
            self.db.add(admin_user)
            self.users.append(admin_user)

            # Create regular users
            for i in range(DATA_CONFIG['users_per_hospital'] - 1):
                user = User(
                    hospital_id=hospital.id,
                    email=f"user{i+1}@{hospital.domain}",
                    password_hash=bcrypt.hashpw("user123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
                    role="user"
                )
                self.db.add(user)
                self.users.append(user)

        self.db.flush()
        print(f"✅ Generated {len(self.users)} users")

    def generate_patients(self):
        """Generate patient data"""
        print("🏥 Generating patients...")

        insurance_providers = [
            "Blue Cross Blue Shield", "Aetna", "Cigna", "UnitedHealth",
            "Humana", "Kaiser Permanente", "Anthem", "Medicare", "Medicaid"
        ]

        for hospital in tqdm(self.hospitals):
            for _ in range(DATA_CONFIG['patients_per_hospital']):
                patient = Patient(
                    hospital_id=hospital.id,
                    name=self.truncate_string(self.fake.name(), 255),
                    email=self.truncate_string(self.fake.email(), 255),
                    phone=self.generate_phone_number(),
                    date_of_birth=self.fake.date_of_birth(minimum_age=1, maximum_age=90),
                    medical_record_number=f"MRN{random.randint(100000, 999999)}",
                    emergency_contact=self.truncate_string(f"{self.fake.name()} - {self.generate_phone_number()}", 255),
                    insurance_provider=self.truncate_string(random.choice(insurance_providers), 100)
                )
                self.db.add(patient)
                self.patients.append(patient)

        self.db.flush()
        print(f"✅ Generated {len(self.patients)} patients")

    def generate_appointments(self):
        """Generate appointment data"""
        print("📅 Generating appointments...")

        statuses = ['pending', 'confirmed', 'completed', 'cancelled']
        status_weights = [0.3, 0.4, 0.25, 0.05]

        departments = [
            "Emergency", "Cardiology", "Pediatrics", "Surgery",
            "Internal Medicine", "Orthopedics", "Neurology",
            "Oncology", "Radiology", "Pathology"
        ]

        appointments = []
        for hospital in tqdm(self.hospitals):
            hospital_patients = [p for p in self.patients if p.hospital_id == hospital.id]

            for _ in range(DATA_CONFIG['appointments_per_hospital']):
                patient = random.choice(hospital_patients)

                # Generate appointment date (past 30 days to future 60 days)
                appointment_date = self.fake.date_between(
                    start_date='-30d',
                    end_date='+60d'
                )

                appointment = Appointment(
                    hospital_id=hospital.id,
                    patient_name=self.truncate_string(patient.name, 255),
                    patient_email=self.truncate_string(patient.email, 255),
                    patient_phone=self.generate_phone_number(),
                    department=self.truncate_string(random.choice(departments), 100),
                    preferred_date=appointment_date,
                    preferred_time=time(
                        hour=random.randint(8, 17),
                        minute=random.choice([0, 15, 30, 45])
                    ),
                    status=random.choices(statuses, weights=status_weights)[0]
                )
                self.db.add(appointment)
                appointments.append(appointment)

        self.db.flush()
        print(f"✅ Generated {len(appointments)} appointments")

    def generate_chat_sessions(self):
        """Generate chat sessions and messages"""
        print("💬 Generating chat sessions and messages...")

        sessions = []
        messages = []

        for hospital in tqdm(self.hospitals):
            for _ in range(DATA_CONFIG['chat_sessions_per_hospital']):
                # Create session
                session_start = self.fake.date_time_between(
                    start_date='-90d',
                    end_date='now'
                )

                session = ChatSession(
                    hospital_id=hospital.id,
                    visitor_id=f"visitor_{uuid.uuid4().hex[:8]}",
                    created_at=session_start,
                    ended_at=session_start + timedelta(minutes=random.randint(2, 30)),
                    session_metadata={
                        "user_agent": self.fake.user_agent(),
                        "ip_address": self.fake.ipv4(),
                        "referrer": self.fake.url()
                    }
                )
                self.db.add(session)
                sessions.append(session)

        self.db.flush()  # Get session IDs

        # Generate messages for each session
        for session in tqdm(sessions, desc="Generating messages"):
            num_messages = random.randint(2, DATA_CONFIG['messages_per_session'] * 2)

            for i in range(num_messages):
                is_user_message = i % 2 == 0  # Alternate between user and bot

                if is_user_message:
                    # User message
                    intent = random.choice(list(CHAT_INTENTS.keys()))
                    message_text = random.choice(CHAT_INTENTS[intent])

                    message = ChatMessage(
                        session_id=session.id,
                        message=self.truncate_string(message_text, 1000),  # Limit message length
                        sender="user",
                        intent=self.truncate_string(intent, 100),
                        confidence=random.uniform(0.7, 0.95),
                        created_at=session.created_at + timedelta(minutes=i)
                    )
                else:
                    # Bot response
                    responses = [
                        "I'd be happy to help you with that.",
                        "Let me check that information for you.",
                        "I can assist you with booking an appointment.",
                        "Here's the information you requested.",
                        "Is there anything else I can help you with?"
                    ]

                    message = ChatMessage(
                        session_id=session.id,
                        message=self.truncate_string(random.choice(responses), 1000),
                        sender="bot",
                        intent=None,
                        confidence=0.9,
                        created_at=session.created_at + timedelta(minutes=i, seconds=30)
                    )

                self.db.add(message)
                messages.append(message)

        self.db.flush()
        print(f"✅ Generated {len(sessions)} chat sessions with {len(messages)} messages")

    def generate_usage_metrics(self):
        """Generate usage metrics"""
        print("📊 Generating usage metrics...")

        metrics = []
        for hospital in tqdm(self.hospitals):
            # Generate daily metrics for the last 90 days
            for i in range(DATA_CONFIG['days_of_history']):
                metric_date = date.today() - timedelta(days=i)

                # Messages metric
                daily_messages = random.randint(10, 200)
                message_metric = UsageMetrics(
                    hospital_id=hospital.id,
                    metric_type='messages',
                    metric_value=daily_messages,
                    date=metric_date
                )
                self.db.add(message_metric)
                metrics.append(message_metric)

                # API calls metric
                api_calls = random.randint(50, 500)
                api_metric = UsageMetrics(
                    hospital_id=hospital.id,
                    metric_type='api_calls',
                    metric_value=api_calls,
                    date=metric_date
                )
                self.db.add(api_metric)
                metrics.append(api_metric)

        self.db.flush()
        print(f"✅ Generated {len(metrics)} usage metrics")

    def generate_subscription_history(self):
        """Generate subscription history"""
        print("💳 Generating subscription history...")

        history_records = []
        for hospital in tqdm(self.hospitals):
            plan_config = SUBSCRIPTION_PLANS[hospital.subscription_plan]

            # Initial subscription
            history = SubscriptionHistory(
                hospital_id=hospital.id,
                plan_name=self.truncate_string(plan_config['name'], 50),
                action='created',
                amount=plan_config['price'],
                currency='USD',
                stripe_invoice_id=self.truncate_string(f"in_{self.fake.lexify(text='??????????')}", 255) if plan_config['price'] > 0 else None
            )
            self.db.add(history)
            history_records.append(history)

            # Random plan changes (for some hospitals)
            if random.random() < 0.3:  # 30% chance of plan change
                new_plan = random.choice(list(SUBSCRIPTION_PLANS.keys()))
                if new_plan != hospital.subscription_plan:
                    new_plan_config = SUBSCRIPTION_PLANS[new_plan]

                    change_history = SubscriptionHistory(
                        hospital_id=hospital.id,
                        plan_name=self.truncate_string(new_plan_config['name'], 50),
                        action='upgraded' if new_plan_config['price'] > plan_config['price'] else 'downgraded',
                        amount=new_plan_config['price'],
                        currency='USD',
                        stripe_invoice_id=self.truncate_string(f"in_{self.fake.lexify(text='??????????')}", 255) if new_plan_config['price'] > 0 else None
                    )
                    self.db.add(change_history)
                    history_records.append(change_history)

        self.db.flush()
        print(f"✅ Generated {len(history_records)} subscription history records")

    def generate_system_configurations(self):
        """Generate system configurations"""
        print("⚙️ Generating system configurations...")

        configs = [
            {
                "key": "maintenance_mode",
                "value": {"enabled": False, "message": "System under maintenance"},
                "description": "System maintenance mode",
                "is_public": True
            },
            {
                "key": "default_subscription_plan",
                "value": {"plan": "trial", "duration_days": 14},
                "description": "Default subscription plan for new hospitals",
                "is_public": False
            },
            {
                "key": "ai_features",
                "value": {
                    "gemini_enabled": True,
                    "rasa_enabled": True,
                    "sentiment_analysis": True
                },
                "description": "AI features configuration",
                "is_public": False
            },
            {
                "key": "rate_limits",
                "value": {
                    "messages_per_minute": 60,
                    "api_calls_per_hour": 1000
                },
                "description": "Rate limiting configuration",
                "is_public": False
            }
        ]

        for config_data in configs:
            # Ensure key length is within limits
            config_data['key'] = self.truncate_string(config_data['key'], 100)
            if config_data.get('description'):
                config_data['description'] = self.truncate_string(config_data['description'], 500)

            config = SystemConfiguration(**config_data)
            self.db.add(config)

        self.db.flush()
        print(f"✅ Generated {len(configs)} system configurations")

    def generate_all_data(self):
        """Generate all dummy data"""
        print("🚀 Starting dummy data generation...")
        print("=" * 50)

        try:
            self.generate_hospitals()
            self.generate_users()
            self.generate_patients()
            self.generate_appointments()
            self.generate_chat_sessions()
            self.generate_usage_metrics()
            self.generate_subscription_history()
            self.generate_system_configurations()

            # Commit all changes
            self.db.commit()

            print("\n🎉 All dummy data generated successfully!")
            self.print_summary()

        except Exception as e:
            print(f"\n❌ Error generating data: {e}")
            self.db.rollback()
            raise
        finally:
            self.db.close()

    def print_summary(self):
        """Print data generation summary"""
        print("\n📊 Data Generation Summary:")
        print(f"   🏥 Hospitals: {len(self.hospitals)}")
        print(f"   👥 Users: {len(self.users)}")
        print(f"   🏥 Patients: {len(self.patients)}")
        print(f"   📅 Appointments: {DATA_CONFIG['appointments_per_hospital'] * len(self.hospitals)}")
        print(f"   💬 Chat Sessions: {DATA_CONFIG['chat_sessions_per_hospital'] * len(self.hospitals)}")
        print(f"   📊 Usage Metrics: {DATA_CONFIG['days_of_history'] * 2 * len(self.hospitals)}")

        print(f"\n🔑 Sample Hospital API Keys:")
        for hospital in self.hospitals[:3]:  # Show first 3
            print(f"   {hospital.name}: {hospital.api_key}")

def main():
    """Main function for standalone execution"""
    generator = DataGenerator()
    generator.generate_all_data()

if __name__ == "__main__":
    main()
