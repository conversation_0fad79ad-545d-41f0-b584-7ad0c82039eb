from datetime import <PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2<PERSON><PERSON><PERSON>R<PERSON><PERSON>Form
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Optional
import uuid

from ...database import get_db
from ...database.models import User, Hospital
from ...auth import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    get_current_user,
    ACCESS_TOKEN_EXPIRE_MINUTES
)

router = APIRouter()

# Pydantic models
class Token(BaseModel):
    access_token: str
    token_type: str

class UserCreate(BaseModel):
    email: EmailStr
    password: str
    role: str = "admin"
    hospital_name: str
    hospital_domain: Optional[str] = None

class UserResponse(BaseModel):
    id: str
    email: str
    role: str
    hospital_id: str
    created_at: str

    class Config:
        from_attributes = True

class HospitalCreate(BaseModel):
    name: str
    domain: Optional[str] = None
    subscription_plan: str = "basic"

@router.post("/register", response_model=dict)
async def register_hospital_and_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """Register a new hospital and admin user"""
    
    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Check if hospital domain already exists
    if user_data.hospital_domain:
        existing_hospital = db.query(Hospital).filter(
            Hospital.domain == user_data.hospital_domain
        ).first()
        if existing_hospital:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Hospital domain already exists"
            )
    
    # Create hospital
    hospital = Hospital(
        name=user_data.hospital_name,
        domain=user_data.hospital_domain,
        api_key=str(uuid.uuid4()),  # Generate unique API key
        subscription_plan="basic"
    )
    db.add(hospital)
    db.flush()  # Get the hospital ID
    
    # Create user
    hashed_password = get_password_hash(user_data.password)
    user = User(
        email=user_data.email,
        password_hash=hashed_password,
        role=user_data.role,
        hospital_id=hospital.id
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    db.refresh(hospital)
    
    return {
        "message": "Hospital and user registered successfully",
        "hospital_id": str(hospital.id),
        "api_key": hospital.api_key,
        "user_id": str(user.id)
    }

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """Get current user information"""
    return UserResponse(
        id=str(current_user.id),
        email=current_user.email,
        role=current_user.role,
        hospital_id=str(current_user.hospital_id),
        created_at=current_user.created_at.isoformat()
    )

@router.post("/refresh-api-key")
async def refresh_api_key(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Refresh the hospital's API key"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin users can refresh API keys"
        )
    
    hospital = db.query(Hospital).filter(Hospital.id == current_user.hospital_id).first()
    if not hospital:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Hospital not found"
        )
    
    # Generate new API key
    hospital.api_key = str(uuid.uuid4())
    db.commit()
    
    return {"api_key": hospital.api_key}
