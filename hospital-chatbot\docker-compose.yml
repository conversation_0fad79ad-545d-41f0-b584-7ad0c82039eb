version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: hospital_chatbot
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: hospital_chatbot_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/hospital_chatbot
      - REDIS_URL=redis://redis:6379/0
      - RASA_URL=http://rasa:5005
      - QDRANT_URL=http://qdrant:6333
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - ./backend:/app
    command: uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

  # Rasa Server
  rasa:
    build:
      context: ./backend/rasa
      dockerfile: Dockerfile
    ports:
      - "5005:5005"
    volumes:
      - ./backend/rasa:/app
    command: rasa run --enable-api --cors "*" --debug

  # Rasa Actions Server
  rasa-actions:
    build:
      context: ./backend/rasa
      dockerfile: Dockerfile.actions
    ports:
      - "5055:5055"
    volumes:
      - ./backend/rasa:/app
    command: rasa run actions

  # Frontend Dashboard
  frontend-dashboard:
    build:
      context: ./frontend/dashboard
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend/dashboard:/app
      - /app/node_modules

  # Frontend Widget
  frontend-widget:
    build:
      context: ./frontend/widget
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend/widget:/app
      - /app/node_modules

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend-dashboard
      - frontend-widget

volumes:
  postgres_data:
  redis_data:
  qdrant_storage:
