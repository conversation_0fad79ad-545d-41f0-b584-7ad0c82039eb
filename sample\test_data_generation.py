#!/usr/bin/env python3
"""
Test Data Generation Script
Tests the data generator with a small dataset to verify it works
"""

import sys
from data_generator import DataGenerator
from config import DATA_CONFIG

def test_small_dataset():
    """Test data generation with a small dataset"""
    print("🧪 Testing data generation with small dataset...")
    
    # Backup original config
    original_config = DATA_CONFIG.copy()
    
    # Use smaller numbers for testing
    DATA_CONFIG['hospitals'] = 2
    DATA_CONFIG['users_per_hospital'] = 2
    DATA_CONFIG['patients_per_hospital'] = 5
    DATA_CONFIG['appointments_per_hospital'] = 10
    DATA_CONFIG['chat_sessions_per_hospital'] = 5
    DATA_CONFIG['messages_per_session'] = 3
    DATA_CONFIG['days_of_history'] = 7
    
    try:
        generator = DataGenerator()
        
        # Test individual generation methods
        print("📋 Testing hospital generation...")
        generator.generate_hospitals()
        
        print("👥 Testing user generation...")
        generator.generate_users()
        
        print("🏥 Testing patient generation...")
        generator.generate_patients()
        
        print("📅 Testing appointment generation...")
        generator.generate_appointments()
        
        print("💬 Testing chat session generation...")
        generator.generate_chat_sessions()
        
        print("📊 Testing usage metrics generation...")
        generator.generate_usage_metrics()
        
        print("💳 Testing subscription history generation...")
        generator.generate_subscription_history()
        
        print("⚙️ Testing system configuration generation...")
        generator.generate_system_configurations()
        
        # Commit the test data
        generator.db.commit()
        
        print("✅ All data generation tests passed!")
        
        # Print summary
        print(f"\n📊 Test Data Summary:")
        print(f"   🏥 Hospitals: {len(generator.hospitals)}")
        print(f"   👥 Users: {len(generator.users)}")
        print(f"   🏥 Patients: {len(generator.patients)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data generation test failed: {e}")
        generator.db.rollback()
        return False
    finally:
        generator.db.close()
        # Restore original config
        DATA_CONFIG.update(original_config)

def main():
    """Main test function"""
    print("🏥 Hospital Chatbot SaaS - Data Generation Test")
    print("=" * 60)
    
    success = test_small_dataset()
    
    if success:
        print("\n🎉 Data generation test completed successfully!")
        print("✅ Ready to run full data generation")
    else:
        print("\n❌ Data generation test failed")
        print("Please check the errors above and fix them")
        sys.exit(1)

if __name__ == "__main__":
    main()
