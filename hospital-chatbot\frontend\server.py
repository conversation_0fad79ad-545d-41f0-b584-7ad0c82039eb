#!/usr/bin/env python3
"""
Simple HTTP Server for Hospital Chatbot SaaS Frontend
Serves the SaaS portal, demo hospital site, and chat widget
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_GET(self):
        # Custom routing
        if self.path == '/':
            self.path = '/saas-portal/index.html'
        elif self.path == '/demo':
            self.path = '/demo-hospital/index.html'
        elif self.path == '/widget.js':
            self.path = '/widget/widget.js'
        
        return super().do_GET()

def start_server(port=3000):
    """Start the frontend server"""
    print("🌐 Starting Hospital Chatbot SaaS Frontend Server")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server running on http://localhost:{port}")
            print()
            print("📋 Available URLs:")
            print(f"   🏢 SaaS Portal:     http://localhost:{port}/")
            print(f"   🏥 Demo Hospital:   http://localhost:{port}/demo")
            print(f"   🤖 Chat Widget:     http://localhost:{port}/widget.js")
            print()
            print("🎯 Features:")
            print("   • Customer registration and login")
            print("   • Subscription plan selection")
            print("   • Integration code generation")
            print("   • Live chat widget demo")
            print("   • Hospital website simulation")
            print()
            print("Press Ctrl+C to stop the server")
            print("=" * 60)
            
            # Open browser automatically
            try:
                webbrowser.open(f'http://localhost:{port}/')
            except:
                pass
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use")
            print(f"💡 Try a different port: python server.py --port {port + 1}")
        else:
            print(f"❌ Server error: {e}")

if __name__ == "__main__":
    import sys
    
    port = 3000
    
    # Check for port argument
    if len(sys.argv) > 1:
        try:
            if sys.argv[1] == '--port' and len(sys.argv) > 2:
                port = int(sys.argv[2])
            else:
                port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 3000.")
    
    start_server(port)
