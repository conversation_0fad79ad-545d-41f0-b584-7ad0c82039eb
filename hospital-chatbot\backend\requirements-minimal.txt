# Minimal requirements for Hospital Chatbot SaaS
# Install these first to get the basic system running

# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database - Compatible with <PERSON>sa
sqlalchemy>=1.4.0,<1.5.0
psycopg2-binary==2.9.9

# Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# Environment and configuration
python-dotenv==1.0.0
pydantic[email]>=1.10.0,<2.0.0

# HTTP client
requests==2.31.0

# AI Integration
google-generativeai==0.3.2

# Additional utilities
python-dateutil==2.8.2
click==8.1.7
typing-extensions>=4.0.0

# Vector Database
qdrant-client==1.7.0
sentence-transformers==2.2.2
