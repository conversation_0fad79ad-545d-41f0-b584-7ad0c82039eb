#!/usr/bin/env python3
"""
Server startup script for Hospital Chatbot SaaS
Handles import path issues and starts the FastAPI server
"""

import sys
import os
import uvicorn
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set environment variables
os.environ.setdefault("PYTHONPATH", str(backend_dir))

def main():
    """Main function to start the server"""
    print("🏥 Starting Hospital Chatbot SaaS Server...")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = backend_dir / ".env"
    if not env_file.exists():
        print("❌ .env file not found!")
        print("💡 Please create .env file in backend directory")
        sys.exit(1)
    
    print("✅ Environment file found")
    print("🚀 Starting FastAPI server...")
    print("📋 API Documentation will be available at: http://localhost:8000/docs")
    print("🔍 Health check available at: http://localhost:8000/health")
    print()
    
    try:
        # Start the server
        uvicorn.run(
            "api.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            reload_dirs=[str(backend_dir)],
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Check if all dependencies are installed: pip install -r requirements.txt")
        print("2. Verify database connection")
        print("3. Check .env file configuration")

if __name__ == "__main__":
    main()
