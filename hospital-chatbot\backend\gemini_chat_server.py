"""
Simple Hospital Chatbot with Gemini Integration
Standalone server for testing Gemini AI functionality
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables
load_dotenv()

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Hospital Chatbot with Gemini AI",
    description="Simple hospital chatbot powered by Google Gemini",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatMessage(BaseModel):
    message: str
    hospital_id: str = "default"
    visitor_id: str = "anonymous"

class ChatResponse(BaseModel):
    response: str
    user_message: str
    hospital_id: str
    visitor_id: str
    model_used: str
    status: str

# Initialize Gemini
gemini_model = None

def initialize_gemini():
    """Initialize Gemini AI model"""
    global gemini_model
    
    try:
        import google.generativeai as genai
        
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key or api_key == "your-gemini-api-key-here":
            print("⚠️  Gemini API key not configured")
            return False
        
        genai.configure(api_key=api_key)
        
        # Try gemini-2.0-flash-exp first, fallback to gemini-pro
        try:
            gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')
            print("✅ Gemini 2.0 Flash Experimental model loaded")
        except:
            gemini_model = genai.GenerativeModel('gemini-pro')
            print("✅ Gemini Pro model loaded")
        
        return True
        
    except ImportError:
        print("❌ google-generativeai package not installed")
        return False
    except Exception as e:
        print(f"❌ Failed to initialize Gemini: {e}")
        return False

# Initialize Gemini on startup
gemini_available = initialize_gemini()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Hospital Chatbot with Gemini AI",
        "gemini_available": gemini_available,
        "endpoints": {
            "chat": "/chat",
            "health": "/health",
            "test_gemini": "/test-gemini"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "gemini_available": gemini_available,
        "model": "gemini-2.0-flash-exp" if gemini_available else "none"
    }

@app.get("/test-gemini")
async def test_gemini():
    """Test Gemini API"""
    if not gemini_available:
        return {
            "status": "error",
            "message": "Gemini not available",
            "instructions": "Configure GOOGLE_API_KEY in .env file"
        }
    
    try:
        response = gemini_model.generate_content("Say 'Hello from Gemini!' and confirm you're working.")
        return {
            "status": "success",
            "test_response": response.text,
            "model": "gemini-2.0-flash-exp"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Gemini test failed: {str(e)}"
        }

@app.post("/chat", response_model=ChatResponse)
async def chat_with_gemini(chat_message: ChatMessage):
    """Chat endpoint with Gemini AI"""
    
    if not gemini_available:
        # Fallback response without Gemini
        fallback_responses = {
            "hello": "Hello! Welcome to our hospital. How can I help you today?",
            "appointment": "I can help you book an appointment. What department would you like to visit?",
            "emergency": "For emergencies, please call 911 or visit our Emergency Department immediately.",
            "hours": "Our visiting hours are 9 AM to 8 PM daily.",
            "departments": "We have Emergency, Cardiology, Pediatrics, Orthopedics, and General Medicine departments."
        }
        
        user_msg = chat_message.message.lower()
        response = "Thank you for your message. Our team will assist you shortly."
        
        for keyword, reply in fallback_responses.items():
            if keyword in user_msg:
                response = reply
                break
        
        return ChatResponse(
            response=response,
            user_message=chat_message.message,
            hospital_id=chat_message.hospital_id,
            visitor_id=chat_message.visitor_id,
            model_used="fallback",
            status="success_fallback"
        )
    
    try:
        # Create hospital-specific prompt
        hospital_prompt = f"""
        You are a helpful AI assistant for a hospital chatbot. You are professional, empathetic, and helpful.
        
        Hospital Information:
        - Name: City General Hospital
        - Departments: Emergency, Cardiology, Pediatrics, Orthopedics, General Medicine
        - Emergency Contact: 911
        - Visiting Hours: 9 AM - 8 PM daily
        - Phone: (*************
        
        Guidelines:
        1. Be empathetic and professional
        2. Help with appointment booking, general information, and directions
        3. For medical emergencies, direct to call 911 or visit Emergency Department
        4. Do NOT provide medical diagnosis or treatment advice
        5. For specific medical questions, suggest consulting with healthcare professionals
        6. Keep responses concise but helpful
        
        Patient message: "{chat_message.message}"
        
        Provide a helpful response:
        """
        
        # Generate response with Gemini
        response = gemini_model.generate_content(hospital_prompt)
        
        return ChatResponse(
            response=response.text,
            user_message=chat_message.message,
            hospital_id=chat_message.hospital_id,
            visitor_id=chat_message.visitor_id,
            model_used="gemini-2.0-flash-exp",
            status="success"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Chat processing failed: {str(e)}"
        )

if __name__ == "__main__":
    print("🏥 Hospital Chatbot with Gemini AI")
    print("=" * 50)
    
    if gemini_available:
        print("✅ Gemini AI is ready!")
    else:
        print("⚠️  Gemini AI not available - using fallback responses")
        print("💡 Configure GOOGLE_API_KEY in .env to enable Gemini")
    
    print("\n🚀 Starting server...")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("🤖 Test Gemini: http://localhost:8000/test-gemini")
    print("💬 Chat Endpoint: POST to http://localhost:8000/chat")
    print()
    
    uvicorn.run(
        "gemini_chat_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
