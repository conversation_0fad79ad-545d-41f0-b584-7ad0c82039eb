{"name": "hospital-chatbot-dashboard", "version": "1.0.0", "description": "Dashboard for Hospital Chatbot SaaS Platform", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "axios": "^1.3.4", "chart.js": "^4.2.1", "react-chartjs-2": "^5.2.0", "date-fns": "^2.29.3", "react-datepicker": "^4.10.0", "react-table": "^7.8.0", "styled-components": "^5.3.9", "@mui/material": "^5.11.10", "@mui/icons-material": "^5.11.9", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "react-hook-form": "^7.43.5", "react-query": "^3.39.3", "react-toastify": "^9.1.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.36.0", "prettier": "^2.8.4"}, "proxy": "http://localhost:8000"}