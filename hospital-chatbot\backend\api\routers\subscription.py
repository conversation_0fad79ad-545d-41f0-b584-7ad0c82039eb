"""
SaaS Subscription Management Router
Handles subscription plans, billing, and usage tracking
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import List, Optional, Dict, Any
import os
from datetime import datetime, timezone, timedelta

# Optional Stripe import
try:
    import stripe
    STRIPE_AVAILABLE = True
except ImportError:
    STRIPE_AVAILABLE = False
    print("⚠️  Stripe not installed - subscription features will be limited")

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database import get_db
from database.models import Hospital, SubscriptionHistory, UsageMetrics, User
from auth import get_current_user, get_current_hospital

router = APIRouter()

# Configure Stripe (if available)
if STRIPE_AVAILABLE:
    stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
    if not stripe.api_key:
        print("⚠️  Stripe API key not configured")
        STRIPE_AVAILABLE = False

# Subscription plans configuration
SUBSCRIPTION_PLANS = {
    "trial": {
        "name": "Trial",
        "price": 0,
        "message_limit": 1000,
        "features": ["Basic chatbot", "Email support", "Basic analytics"]
    },
    "basic": {
        "name": "Basic",
        "price": 49,
        "message_limit": 5000,
        "features": ["Advanced chatbot", "Email support", "Analytics", "Custom branding"]
    },
    "premium": {
        "name": "Premium",
        "price": 149,
        "message_limit": 20000,
        "features": ["AI-powered chatbot", "Priority support", "Advanced analytics", "HMS integration", "Custom branding"]
    },
    "enterprise": {
        "name": "Enterprise",
        "price": 499,
        "message_limit": 100000,
        "features": ["Full AI suite", "24/7 support", "Custom integrations", "White-label", "Dedicated account manager"]
    }
}

# Pydantic models
class SubscriptionPlan(BaseModel):
    plan_id: str
    name: str
    price: float
    message_limit: int
    features: List[str]

class SubscriptionStatus(BaseModel):
    current_plan: str
    status: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    message_usage: int
    message_limit: int
    usage_percentage: float

class CreateCheckoutSession(BaseModel):
    plan_id: str
    success_url: str
    cancel_url: str

class UsageStats(BaseModel):
    current_month: Dict[str, int]
    last_30_days: List[Dict[str, Any]]
    total_messages: int

@router.get("/plans", response_model=List[SubscriptionPlan])
async def get_subscription_plans():
    """Get available subscription plans"""
    plans = []
    for plan_id, plan_data in SUBSCRIPTION_PLANS.items():
        plans.append(SubscriptionPlan(
            plan_id=plan_id,
            name=plan_data["name"],
            price=plan_data["price"],
            message_limit=plan_data["message_limit"],
            features=plan_data["features"]
        ))
    return plans

@router.get("/status", response_model=SubscriptionStatus)
async def get_subscription_status(
    hospital: Hospital = Depends(get_current_hospital)
):
    """Get current subscription status"""

    usage_percentage = 0
    if hospital.monthly_message_limit > 0:
        usage_percentage = (hospital.monthly_message_count / hospital.monthly_message_limit) * 100

    return SubscriptionStatus(
        current_plan=hospital.subscription_plan,
        status=hospital.subscription_status,
        start_date=hospital.subscription_start_date.isoformat() if hospital.subscription_start_date else None,
        end_date=hospital.subscription_end_date.isoformat() if hospital.subscription_end_date else None,
        message_usage=hospital.monthly_message_count,
        message_limit=hospital.monthly_message_limit,
        usage_percentage=round(usage_percentage, 2)
    )

@router.post("/checkout")
async def create_checkout_session(
    checkout_data: CreateCheckoutSession,
    hospital: Hospital = Depends(get_current_hospital),
    current_user: User = Depends(get_current_user)
):
    """Create Stripe checkout session for subscription"""

    if not STRIPE_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Billing service is not available. Please contact support."
        )

    if checkout_data.plan_id not in SUBSCRIPTION_PLANS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid subscription plan"
        )

    plan = SUBSCRIPTION_PLANS[checkout_data.plan_id]

    try:
        # Create or get Stripe customer
        if not hospital.stripe_customer_id:
            customer = stripe.Customer.create(
                email=current_user.email,
                name=hospital.name,
                metadata={
                    "hospital_id": str(hospital.id),
                    "user_id": str(current_user.id)
                }
            )
            hospital.stripe_customer_id = customer.id

        # Create checkout session
        if plan["price"] == 0:
            # Free trial - no payment needed
            return {"checkout_url": checkout_data.success_url + "?trial=true"}

        checkout_session = stripe.checkout.Session.create(
            customer=hospital.stripe_customer_id,
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'product_data': {
                        'name': f'Hospital Chatbot - {plan["name"]} Plan',
                        'description': f'Monthly subscription for {plan["message_limit"]} messages'
                    },
                    'unit_amount': int(plan["price"] * 100),  # Stripe uses cents
                    'recurring': {
                        'interval': 'month'
                    }
                },
                'quantity': 1,
            }],
            mode='subscription',
            success_url=checkout_data.success_url + '?session_id={CHECKOUT_SESSION_ID}',
            cancel_url=checkout_data.cancel_url,
            metadata={
                'hospital_id': str(hospital.id),
                'plan_id': checkout_data.plan_id
            }
        )

        return {"checkout_url": checkout_session.url}

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment processing error: {str(e)}"
        )

@router.post("/webhook")
async def stripe_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """Handle Stripe webhooks"""

    if not STRIPE_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Billing service is not available"
        )

    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')
    endpoint_secret = os.getenv('STRIPE_WEBHOOK_SECRET')

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid payload")
    except stripe.error.SignatureVerificationError:
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Handle the event
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        await handle_successful_payment(session, db)

    elif event['type'] == 'invoice.payment_succeeded':
        invoice = event['data']['object']
        await handle_subscription_renewal(invoice, db)

    elif event['type'] == 'customer.subscription.deleted':
        subscription = event['data']['object']
        await handle_subscription_cancellation(subscription, db)

    return {"status": "success"}

@router.get("/usage", response_model=UsageStats)
async def get_usage_statistics(
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get usage statistics for the hospital"""

    # Current month usage
    current_month = {
        "messages": hospital.monthly_message_count,
        "limit": hospital.monthly_message_limit
    }

    # Last 30 days usage
    thirty_days_ago = datetime.now(timezone.utc).date() - timedelta(days=30)

    usage_data = db.query(UsageMetrics).filter(
        UsageMetrics.hospital_id == hospital.id,
        UsageMetrics.metric_type == 'messages',
        UsageMetrics.date >= thirty_days_ago
    ).order_by(UsageMetrics.date.asc()).all()

    last_30_days = []
    for usage in usage_data:
        last_30_days.append({
            "date": usage.date.isoformat(),
            "messages": usage.metric_value
        })

    # Total messages
    total_messages = sum(usage.metric_value for usage in usage_data)

    return UsageStats(
        current_month=current_month,
        last_30_days=last_30_days,
        total_messages=total_messages
    )

@router.post("/cancel")
async def cancel_subscription(
    hospital: Hospital = Depends(get_current_hospital),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel current subscription"""

    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin users can cancel subscriptions"
        )

    if not STRIPE_AVAILABLE:
        # For non-Stripe cancellations, just update the status
        hospital.subscription_status = 'cancelled'
        hospital.subscription_end_date = datetime.now(timezone.utc) + timedelta(days=30)

        history = SubscriptionHistory(
            hospital_id=hospital.id,
            plan_name=hospital.subscription_plan,
            action='cancelled',
            amount=0
        )
        db.add(history)
        db.commit()

        return {"message": "Subscription cancelled successfully"}

    try:
        if hospital.stripe_subscription_id:
            # Cancel Stripe subscription
            stripe.Subscription.delete(hospital.stripe_subscription_id)

        # Update hospital record
        hospital.subscription_status = 'cancelled'
        hospital.subscription_end_date = datetime.now(timezone.utc) + timedelta(days=30)  # Grace period

        # Log the cancellation
        history = SubscriptionHistory(
            hospital_id=hospital.id,
            plan_name=hospital.subscription_plan,
            action='cancelled',
            amount=0
        )
        db.add(history)
        db.commit()

        return {"message": "Subscription cancelled successfully"}

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error cancelling subscription: {str(e)}"
        )

# Helper functions
async def handle_successful_payment(session, db: Session):
    """Handle successful payment from Stripe"""
    hospital_id = session['metadata']['hospital_id']
    plan_id = session['metadata']['plan_id']

    hospital = db.query(Hospital).filter(Hospital.id == hospital_id).first()
    if hospital:
        plan = SUBSCRIPTION_PLANS[plan_id]

        # Update hospital subscription
        hospital.subscription_plan = plan_id
        hospital.subscription_status = 'active'
        hospital.subscription_start_date = datetime.now(timezone.utc)
        hospital.subscription_end_date = datetime.now(timezone.utc) + timedelta(days=30)
        hospital.monthly_message_limit = plan["message_limit"]
        hospital.stripe_subscription_id = session.get('subscription')

        # Log the subscription
        history = SubscriptionHistory(
            hospital_id=hospital.id,
            plan_name=plan["name"],
            action='created',
            amount=plan["price"],
            stripe_invoice_id=session.get('invoice')
        )
        db.add(history)
        db.commit()

async def handle_subscription_renewal(invoice, db: Session):
    """Handle subscription renewal"""
    customer_id = invoice['customer']

    hospital = db.query(Hospital).filter(
        Hospital.stripe_customer_id == customer_id
    ).first()

    if hospital:
        # Extend subscription
        hospital.subscription_end_date = datetime.now(timezone.utc) + timedelta(days=30)
        hospital.monthly_message_count = 0  # Reset usage
        hospital.last_reset_date = datetime.now(timezone.utc)

        # Log the renewal
        history = SubscriptionHistory(
            hospital_id=hospital.id,
            plan_name=hospital.subscription_plan,
            action='renewed',
            amount=invoice['amount_paid'] / 100,  # Convert from cents
            stripe_invoice_id=invoice['id']
        )
        db.add(history)
        db.commit()

async def handle_subscription_cancellation(subscription, db: Session):
    """Handle subscription cancellation"""
    customer_id = subscription['customer']

    hospital = db.query(Hospital).filter(
        Hospital.stripe_customer_id == customer_id
    ).first()

    if hospital:
        hospital.subscription_status = 'cancelled'

        # Log the cancellation
        history = SubscriptionHistory(
            hospital_id=hospital.id,
            plan_name=hospital.subscription_plan,
            action='cancelled',
            amount=0
        )
        db.add(history)
        db.commit()
