"""
Enhanced Gemini Service with Qdrant Vector Database
Hospital-specific AI responses using vector search for context
"""

import os
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List

# Load environment variables
load_dotenv()

class EnhancedGeminiService:
    def __init__(self):
        """Initialize enhanced Gemini service with Qdrant"""
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp")
        self.model = None
        
        # Import services
        try:
            from services.qdrant_service import qdrant_service
            self.qdrant = qdrant_service
        except ImportError:
            print("⚠️  Qdrant service not available")
            self.qdrant = None
        
        if not self.api_key or self.api_key == "your-gemini-api-key-here":
            print("⚠️  Gemini API key not configured")
            return
        
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.api_key)
            
            # Try different models in order of preference
            models_to_try = [
                "gemini-2.0-flash-exp",
                "gemini-pro",
                "gemini-1.5-pro"
            ]
            
            for model_name in models_to_try:
                try:
                    self.model = genai.GenerativeModel(model_name)
                    self.model_name = model_name
                    print(f"✅ Enhanced Gemini model initialized: {model_name}")
                    break
                except Exception as e:
                    print(f"⚠️  Failed to load {model_name}: {e}")
                    continue
            
            if not self.model:
                print("❌ Failed to initialize any Gemini model")
                
        except ImportError:
            print("❌ google-generativeai package not installed")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini: {e}")
    
    def is_available(self) -> bool:
        """Check if enhanced Gemini service is available"""
        return self.model is not None
    
    def generate_hospital_response(self, message: str, hospital_id: str, hospital_info: Optional[Dict] = None) -> str:
        """Generate hospital-specific response using vector search + Gemini"""
        if not self.is_available():
            return self._fallback_response(message)
        
        try:
            # Get hospital-specific context from vector database
            context = self._get_hospital_context(hospital_id, message)
            
            # Create enhanced prompt with context
            prompt = self._create_enhanced_prompt(message, hospital_id, hospital_info, context)
            
            # Generate response with Gemini
            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            print(f"⚠️  Enhanced Gemini generation failed: {e}")
            return self._fallback_response(message)
    
    def _get_hospital_context(self, hospital_id: str, query: str) -> List[Dict]:
        """Get relevant hospital-specific context from vector database"""
        if not self.qdrant or not self.qdrant.is_available():
            return []
        
        try:
            # Search for relevant hospital documents
            results = self.qdrant.search_hospital_knowledge(
                hospital_id=hospital_id,
                query=query,
                limit=3  # Top 3 most relevant chunks
            )
            
            return results
            
        except Exception as e:
            print(f"⚠️  Vector search failed: {e}")
            return []
    
    def _create_enhanced_prompt(self, message: str, hospital_id: str, hospital_info: Optional[Dict], context: List[Dict]) -> str:
        """Create enhanced prompt with hospital-specific context"""
        
        # Base hospital information
        hospital_context = ""
        if hospital_info:
            hospital_context = f"""
Hospital Information:
- Name: {hospital_info.get('name', 'Our Hospital')}
- Departments: {', '.join(hospital_info.get('departments', ['General Medicine']))}
- Emergency Contact: {hospital_info.get('emergency_contact', '911')}
- Phone: {hospital_info.get('phone', 'Contact us for phone number')}
"""
        
        # Add vector search context
        knowledge_context = ""
        if context:
            knowledge_context = "\n\nRelevant Hospital Knowledge:\n"
            for i, item in enumerate(context, 1):
                knowledge_context += f"{i}. {item['text']}\n"
        
        prompt = f"""
You are a helpful AI assistant for {hospital_id} hospital chatbot. You are professional, empathetic, and helpful.

{hospital_context}

{knowledge_context}

Guidelines:
1. Be empathetic and professional
2. Use the hospital-specific knowledge provided above to give accurate information
3. For medical emergencies, direct to call 911 or visit Emergency Department
4. Do NOT provide medical diagnosis or treatment advice
5. For specific medical questions, suggest consulting with healthcare professionals
6. Keep responses concise but helpful
7. If you don't have specific information, be honest and direct them to call the hospital

Patient message: "{message}"

Provide a helpful, hospital-specific response using the knowledge provided above:
"""
        return prompt
    
    def _fallback_response(self, message: str) -> str:
        """Provide fallback response when enhanced features are not available"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            return "Hello! Welcome to our hospital. How can I help you today?"
        
        elif any(word in message_lower for word in ["appointment", "book", "schedule"]):
            return "I can help you book an appointment. What department would you like to visit? You can also call us directly to schedule."
        
        elif any(word in message_lower for word in ["emergency", "urgent", "pain", "help"]):
            return "For medical emergencies, please call 911 or visit our Emergency Department immediately. For urgent but non-emergency care, please contact us directly."
        
        elif any(word in message_lower for word in ["hours", "time", "open"]):
            return "Please contact us for current hours and any department-specific schedules."
        
        elif any(word in message_lower for word in ["department", "doctor", "specialist"]):
            return "We have several departments and specialists available. What type of care are you looking for? Please call us for specific doctor information."
        
        elif any(word in message_lower for word in ["insurance", "billing", "cost"]):
            return "For insurance and billing questions, please contact our billing department. We accept most major insurance plans and offer payment assistance programs."
        
        else:
            return "Thank you for contacting us. Our team will assist you shortly. For immediate assistance, please call our main number."
    
    def initialize_hospital_knowledge(self, hospital_id: str, docs_directory: str = None):
        """Initialize hospital knowledge in vector database"""
        if not self.qdrant or not self.qdrant.is_available():
            print("⚠️  Qdrant not available for knowledge initialization")
            return False
        
        if not docs_directory:
            docs_directory = os.path.join(os.path.dirname(__file__), "..", "..", "docs")
        
        return self.qdrant.load_hospital_documents_from_files(hospital_id, docs_directory)
    
    def get_hospital_knowledge_stats(self, hospital_id: str) -> Dict:
        """Get statistics about hospital knowledge in vector database"""
        if not self.qdrant or not self.qdrant.is_available():
            return {"error": "Qdrant not available"}
        
        return self.qdrant.get_hospital_stats(hospital_id)

# Global instance
enhanced_gemini_service = EnhancedGeminiService()
