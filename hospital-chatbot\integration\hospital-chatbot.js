(function() {
  // Hospital Chatbot Widget Integration Script
  
  class HospitalChatbot {
    constructor(config) {
      this.hospitalId = config.hospitalId;
      this.apiKey = config.apiKey;
      this.customizations = config.customizations || {};
      this.init();
    }
    
    init() {
      this.loadStyles();
      this.createWidget();
      this.bindEvents();
    }
    
    loadStyles() {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.hospitalchatbot.com/widget.css';
      document.head.appendChild(link);
    }
    
    createWidget() {
      const widgetContainer = document.createElement('div');
      widgetContainer.id = 'hospital-chatbot-widget';
      document.body.appendChild(widgetContainer);
      
      // Load React widget
      this.loadWidget(widgetContainer);
    }
    
    async loadWidget(container) {
      // Dynamically load React component
      const script = document.createElement('script');
      script.src = 'https://cdn.hospitalchatbot.com/widget.js';
      script.onload = () => {
        window.HospitalChatWidget.render(container, {
          hospitalId: this.hospitalId,
          apiKey: this.apiKey,
          customizations: this.customizations
        });
      };
      document.head.appendChild(script);
    }
  }
  
  // Global initialization function
  window.initHospitalChatbot = function(config) {
    return new HospitalChatbot(config);
  };
})();

// Usage example for hospitals:
/*
<script src="https://cdn.hospitalchatbot.com/integration.js"></script>
<script>
  window.initHospitalChatbot({
    hospitalId: 'your-hospital-id',
    apiKey: 'your-api-key',
    customizations: {
      primaryColor: '#007bff',
      position: 'bottom-right',
      welcomeMessage: 'Welcome to City Hospital!'
    }
  });
</script>
*/