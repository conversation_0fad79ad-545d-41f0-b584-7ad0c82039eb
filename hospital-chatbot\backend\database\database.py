import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    print("⚠️  DATABASE_URL not found in environment, using SQLite fallback")
    DATABASE_URL = "sqlite:///./hospital_chatbot.db"
else:
    print(f"✅ Using database: {DATABASE_URL.split('@')[0]}@***")

# Create engine with optimized settings for SaaS
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
else:
    # PostgreSQL configuration for SaaS
    engine = create_engine(
        DATABASE_URL,
        pool_size=20,  # Number of connections to maintain
        max_overflow=30,  # Additional connections beyond pool_size
        pool_timeout=30,  # Timeout for getting connection from pool
        pool_recycle=3600,  # Recycle connections after 1 hour
        pool_pre_ping=True,  # Validate connections before use
        echo=os.getenv("DEBUG", "False").lower() == "true"  # Log SQL queries in debug mode
    )

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Initialize database
def init_db():
    """Initialize database tables"""
    from .models import Base
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully!")

# Drop all tables (for development)
def drop_db():
    """Drop all database tables"""
    from .models import Base
    Base.metadata.drop_all(bind=engine)
    print("Database tables dropped!")
