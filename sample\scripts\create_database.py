#!/usr/bin/env python3
"""
Create Database Only <PERSON>ript
Creates PostgreSQL database and user without tables or data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database_creator import DatabaseCreator

def main():
    """Create database and user only"""
    print("🗄️  Creating PostgreSQL Database and User")
    print("=" * 50)
    
    creator = DatabaseCreator()
    
    if creator.create_database_and_user():
        print("\n✅ Database and user created successfully!")
        print("\nNext steps:")
        print("1. Create tables: python scripts/create_tables.py")
        print("2. Generate data: python scripts/generate_dummy.py")
        print("3. Insert data: python scripts/insert_data.py")
    else:
        print("\n❌ Failed to create database and user")
        sys.exit(1)

if __name__ == "__main__":
    main()
