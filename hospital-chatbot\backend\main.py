"""
Hospital Chatbot SaaS - Main Application Entry Point
Standalone version without relative imports
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import modules after setting up path
from database import init_db

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting Hospital Chatbot SaaS API...")
    # Initialize database tables
    try:
        init_db()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"⚠️  Database initialization warning: {e}")
    yield
    # Shutdown
    print("Shutting down Hospital Chatbot SaaS API...")

# Create FastAPI app
app = FastAPI(
    title="Hospital Chatbot SaaS API",
    description="A comprehensive SaaS platform for hospital chatbots with AI-powered conversations",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Hospital Chatbot SaaS API",
        "version": "1.0.0"
    }

# Import and include routers
try:
    from api.routers import chat, auth, hospital, dashboard, subscription
    
    app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
    app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
    app.include_router(hospital.router, prefix="/api/hospital", tags=["hospital"])
    app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])
    app.include_router(subscription.router, prefix="/api/subscription", tags=["subscription"])
    
    print("✅ All routers loaded successfully")
    
except ImportError as e:
    print(f"⚠️  Router import warning: {e}")
    print("Some features may not be available")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to Hospital Chatbot SaaS API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "features": [
            "AI-powered chat conversations",
            "Multi-tenant hospital management",
            "Subscription billing",
            "Analytics dashboard",
            "HMS integrations"
        ]
    }

if __name__ == "__main__":
    print("🏥 Hospital Chatbot SaaS - Starting Server")
    print("=" * 50)
    
    # Check environment
    env_file = current_dir / ".env"
    if env_file.exists():
        print("✅ Environment file found")
    else:
        print("⚠️  .env file not found - using defaults")
    
    print("🚀 Starting server...")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
