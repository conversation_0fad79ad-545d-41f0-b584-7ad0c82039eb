"""
Hospital Chatbot SaaS - Main Application Entry Point
Standalone version without relative imports
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import modules after setting up path
from database import init_db

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting Hospital Chatbot SaaS API...")
    # Initialize database tables
    try:
        init_db()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"⚠️  Database initialization warning: {e}")
    yield
    # Shutdown
    print("Shutting down Hospital Chatbot SaaS API...")

# Create FastAPI app
app = FastAPI(
    title="Hospital Chatbot SaaS API",
    description="A comprehensive SaaS platform for hospital chatbots with AI-powered conversations",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Hospital Chatbot SaaS API",
        "version": "1.0.0"
    }

# Import and include routers one by one with error handling
routers_loaded = []

# Try to load each router individually
router_configs = [
    ("chat", "/api/chat", ["chat"]),
    ("auth", "/api/auth", ["authentication"]),
    ("hospital", "/api/hospital", ["hospital"]),
    ("dashboard", "/api/dashboard", ["dashboard"]),
    ("subscription", "/api/subscription", ["subscription"])
]

for router_name, prefix, tags in router_configs:
    try:
        if router_name == "chat":
            from api.routers import chat
            app.include_router(chat.router, prefix=prefix, tags=tags)
        elif router_name == "auth":
            from api.routers import auth
            app.include_router(auth.router, prefix=prefix, tags=tags)
        elif router_name == "hospital":
            from api.routers import hospital
            app.include_router(hospital.router, prefix=prefix, tags=tags)
        elif router_name == "dashboard":
            from api.routers import dashboard
            app.include_router(dashboard.router, prefix=prefix, tags=tags)
        elif router_name == "subscription":
            from api.routers import subscription
            app.include_router(subscription.router, prefix=prefix, tags=tags)

        routers_loaded.append(router_name)
        print(f"✅ {router_name} router loaded successfully")

    except ImportError as e:
        print(f"⚠️  Failed to load {router_name} router: {e}")
    except Exception as e:
        print(f"⚠️  Error loading {router_name} router: {e}")

print(f"📋 Loaded {len(routers_loaded)} out of {len(router_configs)} routers: {', '.join(routers_loaded)}")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to Hospital Chatbot SaaS API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "features": [
            "AI-powered chat conversations",
            "Multi-tenant hospital management",
            "Subscription billing",
            "Analytics dashboard",
            "HMS integrations"
        ]
    }

if __name__ == "__main__":
    print("🏥 Hospital Chatbot SaaS - Starting Server")
    print("=" * 50)

    # Check environment
    env_file = current_dir / ".env"
    if env_file.exists():
        print("✅ Environment file found")
    else:
        print("⚠️  .env file not found - using defaults")

    print("🚀 Starting server...")
    print("📋 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print()

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
