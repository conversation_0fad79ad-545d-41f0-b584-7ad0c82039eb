from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import List, Optional, Dict, Any
from datetime import date, time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database import get_db
from database.models import Hospital, User, Appointment, Patient, IntegrationSetting
from auth import get_current_user, get_current_hospital

router = APIRouter()

# Pydantic models
class HospitalResponse(BaseModel):
    id: str
    name: str
    domain: Optional[str] = None
    subscription_plan: str
    created_at: str

    class Config:
        from_attributes = True

class HospitalUpdate(BaseModel):
    name: Optional[str] = None
    domain: Optional[str] = None
    subscription_plan: Optional[str] = None

class AppointmentCreate(BaseModel):
    patient_name: str
    patient_email: EmailStr
    patient_phone: str
    department: str
    preferred_date: date
    preferred_time: time

class AppointmentResponse(BaseModel):
    id: str
    patient_name: str
    patient_email: str
    patient_phone: str
    department: str
    preferred_date: str
    preferred_time: str
    status: str
    created_at: str

    class Config:
        from_attributes = True

class PatientCreate(BaseModel):
    name: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    date_of_birth: Optional[date] = None
    medical_record_number: Optional[str] = None
    emergency_contact: Optional[str] = None
    insurance_provider: Optional[str] = None

class PatientResponse(BaseModel):
    id: str
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    date_of_birth: Optional[str] = None
    medical_record_number: Optional[str] = None
    emergency_contact: Optional[str] = None
    insurance_provider: Optional[str] = None
    created_at: str

    class Config:
        from_attributes = True

class IntegrationCreate(BaseModel):
    hms_type: str  # 'epic', 'cerner', 'custom', etc.
    api_endpoint: Optional[str] = None
    api_credentials: Optional[Dict[str, Any]] = None
    webhook_url: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None

class IntegrationResponse(BaseModel):
    id: str
    hms_type: str
    api_endpoint: Optional[str] = None
    webhook_url: Optional[str] = None
    is_active: bool
    settings: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

@router.get("/info", response_model=HospitalResponse)
async def get_hospital_info(
    hospital: Hospital = Depends(get_current_hospital)
):
    """Get hospital information"""
    return HospitalResponse(
        id=str(hospital.id),
        name=hospital.name,
        domain=hospital.domain,
        subscription_plan=hospital.subscription_plan,
        created_at=hospital.created_at.isoformat()
    )

@router.put("/info", response_model=HospitalResponse)
async def update_hospital_info(
    hospital_update: HospitalUpdate,
    current_user: User = Depends(get_current_user),
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Update hospital information"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin users can update hospital information"
        )

    # Check if domain is already taken
    if hospital_update.domain and hospital_update.domain != hospital.domain:
        existing = db.query(Hospital).filter(
            Hospital.domain == hospital_update.domain,
            Hospital.id != hospital.id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Domain already exists"
            )

    # Update fields
    if hospital_update.name is not None:
        hospital.name = hospital_update.name
    if hospital_update.domain is not None:
        hospital.domain = hospital_update.domain
    if hospital_update.subscription_plan is not None:
        hospital.subscription_plan = hospital_update.subscription_plan

    db.commit()
    db.refresh(hospital)

    return HospitalResponse(
        id=str(hospital.id),
        name=hospital.name,
        domain=hospital.domain,
        subscription_plan=hospital.subscription_plan,
        created_at=hospital.created_at.isoformat()
    )

@router.post("/appointments", response_model=AppointmentResponse)
async def create_appointment(
    appointment_data: AppointmentCreate,
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Create a new appointment"""
    appointment = Appointment(
        hospital_id=hospital.id,
        patient_name=appointment_data.patient_name,
        patient_email=appointment_data.patient_email,
        patient_phone=appointment_data.patient_phone,
        department=appointment_data.department,
        preferred_date=appointment_data.preferred_date,
        preferred_time=appointment_data.preferred_time,
        status="pending"
    )

    db.add(appointment)
    db.commit()
    db.refresh(appointment)

    return AppointmentResponse(
        id=str(appointment.id),
        patient_name=appointment.patient_name,
        patient_email=appointment.patient_email,
        patient_phone=appointment.patient_phone,
        department=appointment.department,
        preferred_date=appointment.preferred_date.isoformat(),
        preferred_time=appointment.preferred_time.isoformat(),
        status=appointment.status,
        created_at=appointment.created_at.isoformat()
    )

@router.get("/appointments", response_model=List[AppointmentResponse])
async def get_appointments(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get hospital appointments"""
    query = db.query(Appointment).filter(Appointment.hospital_id == hospital.id)

    if status:
        query = query.filter(Appointment.status == status)

    appointments = query.order_by(Appointment.created_at.desc()).offset(offset).limit(limit).all()

    return [
        AppointmentResponse(
            id=str(apt.id),
            patient_name=apt.patient_name,
            patient_email=apt.patient_email,
            patient_phone=apt.patient_phone,
            department=apt.department,
            preferred_date=apt.preferred_date.isoformat(),
            preferred_time=apt.preferred_time.isoformat(),
            status=apt.status,
            created_at=apt.created_at.isoformat()
        )
        for apt in appointments
    ]

@router.put("/appointments/{appointment_id}/status")
async def update_appointment_status(
    appointment_id: str,
    status: str,
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Update appointment status"""
    appointment = db.query(Appointment).filter(
        Appointment.id == appointment_id,
        Appointment.hospital_id == hospital.id
    ).first()

    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment not found"
        )

    valid_statuses = ["pending", "confirmed", "cancelled", "completed"]
    if status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
        )

    appointment.status = status
    db.commit()

    return {"message": "Appointment status updated successfully"}

@router.post("/patients", response_model=PatientResponse)
async def create_patient(
    patient_data: PatientCreate,
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Create a new patient record"""
    patient = Patient(
        hospital_id=hospital.id,
        name=patient_data.name,
        email=patient_data.email,
        phone=patient_data.phone,
        date_of_birth=patient_data.date_of_birth,
        medical_record_number=patient_data.medical_record_number,
        emergency_contact=patient_data.emergency_contact,
        insurance_provider=patient_data.insurance_provider
    )

    db.add(patient)
    db.commit()
    db.refresh(patient)

    return PatientResponse(
        id=str(patient.id),
        name=patient.name,
        email=patient.email,
        phone=patient.phone,
        date_of_birth=patient.date_of_birth.isoformat() if patient.date_of_birth else None,
        medical_record_number=patient.medical_record_number,
        emergency_contact=patient.emergency_contact,
        insurance_provider=patient.insurance_provider,
        created_at=patient.created_at.isoformat()
    )

@router.get("/patients", response_model=List[PatientResponse])
async def get_patients(
    limit: int = 50,
    offset: int = 0,
    search: Optional[str] = None,
    hospital: Hospital = Depends(get_current_hospital),
    db: Session = Depends(get_db)
):
    """Get hospital patients"""
    query = db.query(Patient).filter(Patient.hospital_id == hospital.id)

    if search:
        query = query.filter(
            Patient.name.ilike(f"%{search}%") |
            Patient.email.ilike(f"%{search}%") |
            Patient.medical_record_number.ilike(f"%{search}%")
        )

    patients = query.order_by(Patient.created_at.desc()).offset(offset).limit(limit).all()

    return [
        PatientResponse(
            id=str(patient.id),
            name=patient.name,
            email=patient.email,
            phone=patient.phone,
            date_of_birth=patient.date_of_birth.isoformat() if patient.date_of_birth else None,
            medical_record_number=patient.medical_record_number,
            emergency_contact=patient.emergency_contact,
            insurance_provider=patient.insurance_provider,
            created_at=patient.created_at.isoformat()
        )
        for patient in patients
    ]
