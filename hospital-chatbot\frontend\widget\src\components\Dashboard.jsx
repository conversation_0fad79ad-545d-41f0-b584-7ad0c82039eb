import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Sidebar from './Sidebar';
import Analytics from './Analytics';
import Settings from './Settings';
import Integration from './Integration';
import ChatHistory from './ChatHistory';

const Dashboard = () => {
  const [user, setUser] = useState(null);
  const [hospitalData, setHospitalData] = useState(null);

  useEffect(() => {
    // Load user and hospital data
    fetchUserData();
  }, []);

  return (
    <Router>
      <div className="dashboard-container">
        <Sidebar />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<Analytics />} />
            <Route path="/chat-history" element={<ChatHistory />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/integration" element={<Integration />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
};