#!/usr/bin/env python3
"""
Complete SaaS Setup Script for Hospital Chatbot
This script sets up PostgreSQL, creates sample data, and configures the SaaS platform
"""

import os
import sys
import subprocess
import asyncio
import uuid
from datetime import datetime, timezone, timedelta

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", 
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "backend", "requirements.txt")
        ], check=True)
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    
    return True

def setup_environment():
    """Set up environment variables"""
    print("🔧 Setting up environment variables...")
    
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "backend", ".env")
    
    # Check if .env already exists
    if os.path.exists(env_path):
        response = input("⚠️  .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("ℹ️  Skipping environment setup")
            return True
    
    # Get user input for configuration
    print("\n🔑 Please provide the following configuration:")
    
    # Database configuration
    db_host = input("PostgreSQL host (default: localhost): ").strip() or "localhost"
    db_port = input("PostgreSQL port (default: 5432): ").strip() or "5432"
    db_name = input("Database name (default: hospital_chatbot_saas): ").strip() or "hospital_chatbot_saas"
    db_user = input("Database user (default: hospital_chatbot_user): ").strip() or "hospital_chatbot_user"
    db_password = input("Database password (default: HospitalBot2024!): ").strip() or "HospitalBot2024!"
    
    # API Keys
    print("\n🤖 AI Configuration:")
    gemini_api_key = input("Google Gemini API Key (required): ").strip()
    if not gemini_api_key:
        print("❌ Gemini API key is required for AI features")
        return False
    
    # SaaS Configuration
    print("\n💳 SaaS Configuration (optional for development):")
    stripe_publishable = input("Stripe Publishable Key (optional): ").strip()
    stripe_secret = input("Stripe Secret Key (optional): ").strip()
    stripe_webhook = input("Stripe Webhook Secret (optional): ").strip()
    
    # Generate secure secret key
    secret_key = str(uuid.uuid4()) + str(uuid.uuid4()).replace('-', '')
    
    # Create .env content
    env_content = f"""# Database Configuration for SaaS
DATABASE_URL=postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}

# Security
SECRET_KEY={secret_key}
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Gemini AI Configuration
GOOGLE_API_KEY={gemini_api_key}
GEMINI_MODEL=gemini-2.0-flash-exp

# Rasa Configuration
RASA_URL=http://localhost:5005
RASA_ACTION_ENDPOINT=http://localhost:5055/webhook

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
ENVIRONMENT=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://yourdomain.com

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Email Configuration (for SaaS notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# SaaS Configuration
SAAS_DOMAIN=localhost:8000
STRIPE_PUBLISHABLE_KEY={stripe_publishable}
STRIPE_SECRET_KEY={stripe_secret}
STRIPE_WEBHOOK_SECRET={stripe_webhook}

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn-for-error-tracking
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Logging
LOG_LEVEL=INFO
LOG_FILE=hospital_chatbot_saas.log

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# File Storage (for SaaS)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=hospital-chatbot-saas-files
AWS_REGION=us-east-1
"""
    
    # Write .env file
    with open(env_path, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Environment file created at: {env_path}")
    return True

def setup_database():
    """Set up database and create sample data"""
    print("🗄️  Setting up database...")
    
    try:
        # Import after setting up environment
        from backend.database.database import init_db, SessionLocal
        from backend.database.models import (
            Hospital, User, SystemConfiguration, SubscriptionHistory
        )
        from backend.auth.auth import get_password_hash
        
        # Initialize database tables
        init_db()
        print("✅ Database tables created successfully!")
        
        # Create sample data
        db = SessionLocal()
        
        try:
            # Check if sample data already exists
            existing_hospital = db.query(Hospital).filter(Hospital.name == "Demo Hospital").first()
            if existing_hospital:
                print("ℹ️  Sample data already exists")
                return True
            
            # Create demo hospital with SaaS features
            hospital = Hospital(
                name="Demo Hospital",
                domain="demo-hospital.local",
                api_key=str(uuid.uuid4()),
                subscription_plan="premium",
                subscription_status="active",
                subscription_start_date=datetime.now(timezone.utc),
                subscription_end_date=datetime.now(timezone.utc) + timedelta(days=30),
                monthly_message_limit=20000,
                monthly_message_count=150,
                contact_email="<EMAIL>",
                billing_email="<EMAIL>",
                phone="******-0123",
                address="123 Healthcare Ave, Medical City, MC 12345",
                timezone="America/New_York",
                language="en",
                custom_branding={
                    "primary_color": "#2563eb",
                    "secondary_color": "#1e40af",
                    "logo_url": None,
                    "welcome_message": "Welcome to Demo Hospital! How can I help you today?"
                }
            )
            db.add(hospital)
            db.flush()
            
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                password_hash=get_password_hash("admin123"),
                role="admin",
                hospital_id=hospital.id
            )
            db.add(admin_user)
            
            # Create regular user
            regular_user = User(
                email="<EMAIL>",
                password_hash=get_password_hash("user123"),
                role="user",
                hospital_id=hospital.id
            )
            db.add(regular_user)
            
            # Create system configurations
            configs = [
                {
                    "key": "maintenance_mode",
                    "value": {"enabled": False, "message": "System under maintenance"},
                    "description": "System maintenance mode",
                    "is_public": True
                },
                {
                    "key": "default_subscription_plan",
                    "value": {"plan": "trial", "duration_days": 14},
                    "description": "Default subscription plan for new hospitals",
                    "is_public": False
                },
                {
                    "key": "ai_features",
                    "value": {
                        "gemini_enabled": True,
                        "rasa_enabled": True,
                        "sentiment_analysis": True
                    },
                    "description": "AI features configuration",
                    "is_public": False
                }
            ]
            
            for config_data in configs:
                config = SystemConfiguration(**config_data)
                db.add(config)
            
            # Create subscription history
            history = SubscriptionHistory(
                hospital_id=hospital.id,
                plan_name="Premium",
                action="created",
                amount=149.0,
                currency="USD"
            )
            db.add(history)
            
            db.commit()
            
            print("✅ Sample data created successfully!")
            print(f"🏥 Demo Hospital: {hospital.name}")
            print(f"🔑 API Key: {hospital.api_key}")
            print(f"👤 Admin User: <EMAIL> / admin123")
            print(f"👤 Regular User: <EMAIL> / user123")
            
        except Exception as e:
            db.rollback()
            print(f"❌ Error creating sample data: {e}")
            return False
        finally:
            db.close()
            
        return True
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False

def setup_rasa():
    """Set up and train Rasa model"""
    print("🤖 Setting up Rasa...")
    
    rasa_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "backend", "rasa")
    
    try:
        # Change to rasa directory
        original_dir = os.getcwd()
        os.chdir(rasa_dir)
        
        # Train Rasa model
        print("🏋️  Training Rasa model...")
        result = subprocess.run(["rasa", "train"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Rasa model trained successfully!")
        else:
            print(f"⚠️  Rasa training completed with warnings: {result.stderr}")
        
        os.chdir(original_dir)
        return True
        
    except Exception as e:
        print(f"❌ Error setting up Rasa: {e}")
        os.chdir(original_dir)
        return False

def main():
    """Main setup function"""
    print("🏥 Hospital Chatbot SaaS - Complete Setup")
    print("=" * 50)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Step 2: Set up environment
    if not setup_environment():
        sys.exit(1)
    
    # Step 3: Set up database
    if not setup_database():
        sys.exit(1)
    
    # Step 4: Set up Rasa (optional)
    setup_rasa_response = input("\n🤖 Set up and train Rasa model? (Y/n): ")
    if setup_rasa_response.lower() != 'n':
        setup_rasa()
    
    print("\n🎉 SaaS setup completed successfully!")
    print("\nNext steps:")
    print("1. Start PostgreSQL service if not running")
    print("2. Start Redis service if using Redis features")
    print("3. Run the application:")
    print("   cd backend")
    print("   uvicorn api.main:app --reload")
    print("4. Access the API documentation at: http://localhost:8000/docs")
    print("5. Test the chat API with the demo hospital API key")
    
    print(f"\n📋 Important Information:")
    print(f"- Demo Hospital API Key: Check the output above")
    print(f"- Admin Login: <EMAIL> / admin123")
    print(f"- Environment file: backend/.env")
    print(f"- Update Stripe keys in .env for payment processing")

if __name__ == "__main__":
    main()
