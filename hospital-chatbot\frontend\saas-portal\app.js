// Hospital Chatbot SaaS Portal JavaScript

// Global variables
let currentUser = null;
let selectedPlan = null;

// API Base URL
const API_BASE = 'http://localhost:8000/api';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    checkAuthStatus();
    
    // Add enter key listener for demo chat
    document.getElementById('demoInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendDemoMessage();
        }
    });
});

// Authentication functions
function checkAuthStatus() {
    const token = localStorage.getItem('hospitalbot_token');
    if (token) {
        // Validate token and get user info
        fetch(`${API_BASE}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('Invalid token');
        })
        .then(user => {
            currentUser = user;
            updateNavigation();
        })
        .catch(() => {
            localStorage.removeItem('hospitalbot_token');
        });
    }
}

function updateNavigation() {
    const navItems = document.querySelector('.navbar-nav');
    if (currentUser) {
        navItems.innerHTML = `
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user"></i> ${currentUser.hospital_name}
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="showDashboard()">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showIntegration()">
                        <i class="fas fa-code"></i> Integration
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="showSettings()">
                        <i class="fas fa-cog"></i> Settings
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a></li>
                </ul>
            </li>
        `;
    }
}

// Modal functions
function showModal(title, content, size = 'lg') {
    const modalHtml = `
        <div class="modal fade" id="dynamicModal" tabindex="-1">
            <div class="modal-dialog modal-${size}">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('modalContainer').innerHTML = modalHtml;
    const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
    modal.show();
    
    // Clean up when modal is hidden
    document.getElementById('dynamicModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('modalContainer').innerHTML = '';
    });
}

// Authentication modals
function showLogin() {
    const content = `
        <form id="loginForm">
            <div class="mb-3">
                <label class="form-label">Email</label>
                <input type="email" class="form-control" id="loginEmail" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Password</label>
                <input type="password" class="form-control" id="loginPassword" required>
            </div>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Login</button>
            </div>
            <div class="text-center mt-3">
                <small>Don't have an account? <a href="#" onclick="showSignup()">Sign up here</a></small>
            </div>
        </form>
    `;
    
    showModal('Login to Your Account', content, 'md');
    
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });
}

function showSignup() {
    const content = `
        <form id="signupForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Hospital Name</label>
                        <input type="text" class="form-control" id="hospitalName" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="contactPerson" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" id="signupEmail" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-control" id="signupPassword" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">Hospital Website</label>
                <input type="url" class="form-control" id="website" placeholder="https://yourhospital.com">
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                <label class="form-check-label" for="agreeTerms">
                    I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                </label>
            </div>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Create Account</button>
            </div>
            <div class="text-center mt-3">
                <small>Already have an account? <a href="#" onclick="showLogin()">Login here</a></small>
            </div>
        </form>
    `;
    
    showModal('Create Your Account', content, 'lg');
    
    document.getElementById('signupForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleSignup();
    });
}

// Plan selection
function selectPlan(planType) {
    selectedPlan = planType;
    
    if (currentUser) {
        showUpgradePlan(planType);
    } else {
        showSignup();
    }
}

function showUpgradePlan(planType) {
    const plans = {
        starter: { name: 'Starter', price: 99, conversations: '1,000' },
        professional: { name: 'Professional', price: 299, conversations: '5,000' },
        enterprise: { name: 'Enterprise', price: 799, conversations: 'Unlimited' }
    };
    
    const plan = plans[planType];
    
    const content = `
        <div class="text-center mb-4">
            <h4>Upgrade to ${plan.name} Plan</h4>
            <div class="display-6 fw-bold text-primary">$${plan.price}<small class="fs-6 text-muted">/month</small></div>
            <p class="text-muted">${plan.conversations} conversations per month</p>
        </div>
        
        <form id="upgradeForm">
            <div class="mb-3">
                <label class="form-label">Billing Email</label>
                <input type="email" class="form-control" id="billingEmail" value="${currentUser?.email || ''}" required>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Payment Method</label>
                <div class="border rounded p-3">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="paymentMethod" id="creditCard" value="card" checked>
                        <label class="form-check-label" for="creditCard">
                            <i class="fas fa-credit-card"></i> Credit Card
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="paymentMethod" id="bankTransfer" value="bank">
                        <label class="form-check-label" for="bankTransfer">
                            <i class="fas fa-university"></i> Bank Transfer
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 
                <strong>14-day free trial</strong> - You won't be charged until the trial period ends.
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">Start Free Trial</button>
            </div>
        </form>
    `;
    
    showModal('Choose Your Plan', content, 'md');
    
    document.getElementById('upgradeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handlePlanUpgrade(planType);
    });
}

// Dashboard functions
function showDashboard() {
    if (!currentUser) {
        showLogin();
        return;
    }
    
    const content = `
        <div class="row g-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-comments fa-2x text-primary mb-2"></i>
                        <h5>1,247</h5>
                        <small class="text-muted">Total Conversations</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-success mb-2"></i>
                        <h5>892</h5>
                        <small class="text-muted">Unique Visitors</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-smile fa-2x text-warning mb-2"></i>
                        <h5>94%</h5>
                        <small class="text-muted">Satisfaction Rate</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-info mb-2"></i>
                        <h5>2.3s</h5>
                        <small class="text-muted">Avg Response Time</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Recent Conversations</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Visitor</th>
                                        <th>Topic</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2 min ago</td>
                                        <td>Anonymous</td>
                                        <td>Appointment booking</td>
                                        <td><span class="badge bg-success">Resolved</span></td>
                                    </tr>
                                    <tr>
                                        <td>5 min ago</td>
                                        <td>John D.</td>
                                        <td>Visiting hours</td>
                                        <td><span class="badge bg-success">Resolved</span></td>
                                    </tr>
                                    <tr>
                                        <td>12 min ago</td>
                                        <td>Sarah M.</td>
                                        <td>Insurance inquiry</td>
                                        <td><span class="badge bg-warning">Escalated</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="showIntegration()">
                                <i class="fas fa-code"></i> Get Integration Code
                            </button>
                            <button class="btn btn-outline-success" onclick="showSettings()">
                                <i class="fas fa-cog"></i> Configure Chatbot
                            </button>
                            <button class="btn btn-outline-info" onclick="showAnalytics()">
                                <i class="fas fa-chart-bar"></i> View Analytics
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    showModal('Dashboard', content, 'xl');
}

// Integration code modal
function showIntegration() {
    const apiKey = currentUser?.api_key || 'your-api-key-here';
    const hospitalId = currentUser?.hospital_name || 'Your Hospital';
    
    const embedCode = `<!-- Hospital Chatbot Widget -->
<div id="hospital-chatbot"></div>
<script>
  window.HospitalChatbot = {
    apiKey: '${apiKey}',
    hospitalId: '${hospitalId}',
    apiUrl: 'http://localhost:8000/api',
    theme: {
      primaryColor: '#007bff',
      position: 'bottom-right'
    }
  };
</script>
<script src="http://localhost:8000/widget.js"></script>`;

    const content = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> 
            Your chatbot is ready! Copy the code below and paste it into your website.
        </div>
        
        <div class="mb-3">
            <label class="form-label fw-bold">Embed Code</label>
            <textarea class="form-control" rows="10" readonly id="embedCode">${embedCode}</textarea>
        </div>
        
        <div class="d-flex gap-2">
            <button class="btn btn-primary" onclick="copyEmbedCode()">
                <i class="fas fa-copy"></i> Copy Code
            </button>
            <button class="btn btn-outline-secondary" onclick="testChatbot()">
                <i class="fas fa-play"></i> Test Chatbot
            </button>
        </div>
        
        <hr>
        
        <h6>Integration Instructions:</h6>
        <ol>
            <li>Copy the embed code above</li>
            <li>Paste it before the closing <code>&lt;/body&gt;</code> tag on your website</li>
            <li>The chatbot will appear automatically on your site</li>
            <li>Customize the appearance in the Settings section</li>
        </ol>
    `;
    
    showModal('Integration Code', content, 'lg');
}

// Demo chat functionality
function sendDemoMessage() {
    const input = document.getElementById('demoInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addDemoMessage(message, 'user');
    input.value = '';
    
    // Simulate bot response
    setTimeout(() => {
        const responses = {
            'hello': 'Hello! Welcome to City General Hospital. How can I assist you today?',
            'hours': 'Our visiting hours are 9:00 AM - 8:00 PM daily. ICU visiting hours are 2:00 PM - 4:00 PM and 6:00 PM - 8:00 PM.',
            'appointment': 'I can help you book an appointment. Please call (************* or visit our online portal. What department would you like to visit?',
            'emergency': 'For medical emergencies, please call 911 immediately or visit our Emergency Department on the east side of the building.',
            'insurance': 'We accept most major insurance plans including Blue Cross Blue Shield, Aetna, Cigna, Medicare, and Medicaid. Please contact our billing department for specific coverage questions.'
        };
        
        let response = "Thank you for your message. Our team will assist you shortly. For immediate assistance, please call (*************.";
        
        for (let key in responses) {
            if (message.toLowerCase().includes(key)) {
                response = responses[key];
                break;
            }
        }
        
        addDemoMessage(response, 'bot');
    }, 1000);
}

function addDemoMessage(message, sender) {
    const chatDemo = document.getElementById('chatDemo');
    const isBot = sender === 'bot';
    
    const messageHtml = `
        <div class="chat-message ${sender}-message mb-3">
            <div class="d-flex ${isBot ? '' : 'justify-content-end'}">
                ${isBot ? `
                    <div class="avatar bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-robot"></i>
                    </div>
                ` : ''}
                <div class="message-content">
                    <div class="${isBot ? 'bg-light' : 'bg-primary text-white'} p-3 rounded">
                        ${message}
                    </div>
                    <small class="text-muted">Just now</small>
                </div>
                ${!isBot ? `
                    <div class="avatar bg-secondary text-white rounded-circle ms-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user"></i>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
    
    chatDemo.insertAdjacentHTML('beforeend', messageHtml);
    chatDemo.scrollTop = chatDemo.scrollHeight;
}

// Utility functions
function copyEmbedCode() {
    const embedCode = document.getElementById('embedCode');
    embedCode.select();
    document.execCommand('copy');
    
    // Show success message
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
    btn.classList.add('btn-success');
    btn.classList.remove('btn-primary');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.add('btn-primary');
        btn.classList.remove('btn-success');
    }, 2000);
}

// Placeholder functions for API calls
function handleLogin() {
    // Simulate login
    console.log('Login attempted');
    // In real implementation, make API call to authenticate
}

function handleSignup() {
    // Simulate signup
    console.log('Signup attempted');
    // In real implementation, make API call to create account
}

function handlePlanUpgrade(planType) {
    // Simulate plan upgrade
    console.log('Plan upgrade attempted:', planType);
    // In real implementation, integrate with Stripe
}

function showSettings() {
    console.log('Show settings');
}

function showAnalytics() {
    console.log('Show analytics');
}

function testChatbot() {
    console.log('Test chatbot');
}

function logout() {
    localStorage.removeItem('hospitalbot_token');
    currentUser = null;
    location.reload();
}
