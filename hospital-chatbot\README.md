# Hospital Chatbot SaaS Platform

A comprehensive AI-powered chatbot SaaS solution designed specifically for hospitals and healthcare providers. This platform enables hospitals to integrate intelligent chatbots powered by **Gemini 2.0 Flash** into their websites for patient engagement, appointment booking, symptom checking, and general inquiries.

## 🚀 SaaS Features

- **Multi-tenant Architecture**: Support for unlimited hospitals with isolated data
- **Subscription Management**: Integrated Stripe billing with multiple plans
- **Usage Tracking**: Real-time monitoring of API usage and limits
- **AI-Powered Conversations**: Gemini 2.0 Flash for intelligent responses
- **Custom Branding**: Hospital-specific customization and white-labeling
- **Analytics Dashboard**: Comprehensive insights and reporting
- **HMS Integration**: Connect with Epic, Cerner, and custom systems

## Features

### 🏥 Hospital Management
- Multi-tenant architecture supporting multiple hospitals
- Hospital-specific customization and branding
- API key-based authentication for secure integration
- Subscription-based access control

### 🤖 AI-Powered Chatbot
- Natural Language Understanding using Rasa
- Hospital-specific intent recognition
- Patient registration and appointment booking
- Symptom checking and department information
- Emergency contact assistance
- Insurance inquiry handling

### 📊 Analytics Dashboard
- Real-time conversation analytics
- Intent analysis and confidence metrics
- Appointment booking statistics
- Patient engagement insights
- Exportable conversation data

### 🔗 HMS Integration
- Epic FHIR integration
- Cerner FHIR support
- Custom HMS webhook support
- Flexible adapter pattern for new integrations

### 🎨 Customizable Widget
- Embeddable chat widget for hospital websites
- Customizable colors and branding
- Responsive design for all devices
- Easy integration with single script tag

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Rasa NLU/Core │
│   Dashboard     │◄──►│   (FastAPI)     │◄──►│   + Actions     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chat Widget   │◄──►│   Database      │    │   HMS           │
│   (React)       │    │   (PostgreSQL)  │    │   Integrations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites
- Python 3.10+
- Node.js 16+
- PostgreSQL 15+ (installed locally or via Docker)
- Redis (optional, for caching)
- Google Gemini API Key
- Stripe Account (for SaaS billing)

### 🚀 Quick SaaS Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hospital-chatbot
   ```

2. **Run the automated SaaS setup**
   ```bash
   python scripts/setup_saas.py
   ```

   This script will:
   - Install all Python dependencies
   - Configure PostgreSQL database
   - Set up environment variables
   - Create sample hospital data
   - Train the Rasa model

3. **Start the application**
   ```bash
   cd backend
   uvicorn api.main:app --reload
   ```

4. **Access the services**
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health
   - Test with demo hospital API key (provided in setup output)

### Using Docker (Alternative)

1. **Clone and configure**
   ```bash
   git clone <repository-url>
   cd hospital-chatbot
   cp backend/.env.example backend/.env
   # Edit .env with your configuration
   ```

2. **Start all services**
   ```bash
   docker-compose up -d
   ```

3. **Initialize the database**
   ```bash
   docker-compose exec backend python scripts/init_db.py
   ```

### Manual Setup

#### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   python scripts/init_db.py
   ```

6. **Start the API server**
   ```bash
   uvicorn api.main:app --reload
   ```

#### Rasa Setup

1. **Navigate to Rasa directory**
   ```bash
   cd backend/rasa
   ```

2. **Train the model**
   ```bash
   rasa train
   ```

3. **Start Rasa server**
   ```bash
   rasa run --enable-api --cors "*"
   ```

4. **Start Rasa actions server** (in another terminal)
   ```bash
   rasa run actions
   ```

#### Frontend Setup

1. **Dashboard setup**
   ```bash
   cd frontend/dashboard
   npm install
   npm start
   ```

2. **Widget setup**
   ```bash
   cd frontend/widget
   npm install
   npm start
   ```

## API Documentation

### Authentication

The API uses JWT tokens for user authentication and API keys for hospital integration.

#### Register Hospital and Admin User
```bash
POST /api/auth/register
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "hospital_name": "City Hospital",
  "hospital_domain": "cityhospital.com"
}
```

#### Login
```bash
POST /api/auth/login
{
  "username": "<EMAIL>",
  "password": "secure_password"
}
```

### Chat API

#### Send Message
```bash
POST /api/chat/message
Headers: Authorization: Bearer <api_key>
{
  "message": "I want to book an appointment",
  "session_id": "optional-session-id",
  "visitor_id": "unique-visitor-id"
}
```

### Hospital Management

#### Get Hospital Info
```bash
GET /api/hospital/info
Headers: Authorization: Bearer <jwt_token>
```

#### Create Appointment
```bash
POST /api/hospital/appointments
Headers: Authorization: Bearer <jwt_token>
{
  "patient_name": "John Doe",
  "patient_email": "<EMAIL>",
  "patient_phone": "+**********",
  "department": "Cardiology",
  "preferred_date": "2024-01-15",
  "preferred_time": "10:00"
}
```

## Widget Integration

Add the chatbot to any website with a simple script tag:

```html
<script src="https://your-domain.com/integration/hospital-chatbot.js"></script>
<script>
  window.initHospitalChatbot({
    hospitalId: 'your-hospital-id',
    apiKey: 'your-api-key',
    customizations: {
      primaryColor: '#007bff',
      position: 'bottom-right',
      welcomeMessage: 'Welcome to City Hospital!'
    }
  });
</script>
```

## Configuration

### Environment Variables

Key environment variables for configuration:

- `DATABASE_URL`: Database connection string
- `SECRET_KEY`: JWT secret key
- `RASA_URL`: Rasa server URL
- `ALLOWED_ORIGINS`: CORS allowed origins

### Rasa Configuration

The Rasa model can be customized by modifying:
- `backend/rasa/data/nlu.yml`: Training examples
- `backend/rasa/data/stories.yml`: Conversation flows
- `backend/rasa/domain.yml`: Intents, entities, and responses
- `backend/rasa/config.yml`: NLU and Core configuration

## Development

### Adding New Intents

1. Add training examples to `backend/rasa/data/nlu.yml`
2. Create stories in `backend/rasa/data/stories.yml`
3. Add responses to `backend/rasa/domain.yml`
4. Implement custom actions in `backend/rasa/actions/actions.py`
5. Retrain the model: `rasa train`

### Adding New API Endpoints

1. Create new router in `backend/api/routers/`
2. Add database models if needed in `backend/database/models.py`
3. Include router in `backend/api/main.py`

### Testing

Run tests with:
```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend/dashboard
npm test
```

## Deployment

### Production Deployment

1. **Set up production environment variables**
2. **Use PostgreSQL for database**
3. **Set up SSL certificates**
4. **Configure reverse proxy (Nginx)**
5. **Set up monitoring and logging**

### Scaling Considerations

- Use Redis for session storage in multi-instance deployments
- Implement database connection pooling
- Use CDN for static assets
- Consider horizontal scaling for Rasa servers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Contact the development team

## Roadmap

- [ ] Voice chat integration
- [ ] Multi-language support
- [ ] Advanced analytics
- [ ] Mobile app
- [ ] Telemedicine integration
- [ ] AI-powered symptom assessment
