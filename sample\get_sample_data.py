#!/usr/bin/env python3
"""
Get Sample Data Script
Retrieves sample hospital data and API keys for testing
"""

from sqlalchemy import create_engine, text
from config import get_database_url

def get_sample_hospitals():
    """Get sample hospital data for testing"""
    print("🏥 Sample Hospital Data for Testing")
    print("=" * 60)
    
    try:
        engine = create_engine(get_database_url())
        
        with engine.connect() as conn:
            # Get hospital data
            result = conn.execute(text("""
                SELECT name, domain, api_key, subscription_plan, 
                       contact_email, monthly_message_count, monthly_message_limit
                FROM hospitals 
                ORDER BY name
            """))
            
            hospitals = result.fetchall()
            
            print(f"📊 Found {len(hospitals)} hospitals in database:\n")
            
            for i, hospital in enumerate(hospitals, 1):
                name, domain, api_key, plan, email, used, limit = hospital
                usage_pct = (used / limit * 100) if limit > 0 else 0
                
                print(f"{i}. **{name}**")
                print(f"   Domain: {domain}")
                print(f"   Plan: {plan.title()}")
                print(f"   API Key: {api_key}")
                print(f"   Contact: {email}")
                print(f"   Usage: {used:,}/{limit:,} messages ({usage_pct:.1f}%)")
                print()
            
            # Get user credentials
            print("👥 Sample User Credentials:")
            print("=" * 40)
            
            result = conn.execute(text("""
                SELECT h.name, u.email, u.role
                FROM users u
                JOIN hospitals h ON u.hospital_id = h.id
                WHERE u.role = 'admin'
                ORDER BY h.name
                LIMIT 5
            """))
            
            users = result.fetchall()
            
            for hospital_name, email, role in users:
                print(f"🏥 {hospital_name}")
                print(f"   Admin: {email} / admin123")
                print(f"   Regular: user1@{email.split('@')[1]} / user123")
                print()
            
            return hospitals
            
    except Exception as e:
        print(f"❌ Error retrieving sample data: {e}")
        return []

def generate_test_commands(hospitals):
    """Generate test commands for the API"""
    if not hospitals:
        return
    
    print("🧪 API Testing Commands")
    print("=" * 40)
    
    # Use first hospital for testing
    hospital = hospitals[0]
    name, domain, api_key, plan, email, used, limit = hospital
    
    print(f"Using: {name} (API Key: {api_key[:20]}...)")
    print()
    
    print("1. **Test Chat API:**")
    print(f"""curl -X POST "http://localhost:8000/api/chat/message" \\
     -H "Authorization: Bearer {api_key}" \\
     -H "Content-Type: application/json" \\
     -d '{{"message": "Hello, I need help booking an appointment", "visitor_id": "test_visitor_123"}}'""")
    print()
    
    print("2. **Test Authentication:**")
    print(f"""curl -X POST "http://localhost:8000/api/auth/login" \\
     -H "Content-Type: application/x-www-form-urlencoded" \\
     -d "username={email}&password=admin123" """)
    print()
    
    print("3. **Test Hospital Info (requires JWT token from login):**")
    print("""curl -X GET "http://localhost:8000/api/hospital/info" \\
     -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" """)
    print()
    
    print("4. **Test Dashboard Stats:**")
    print("""curl -X GET "http://localhost:8000/api/dashboard/stats" \\
     -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" """)
    print()
    
    print("5. **Test Subscription Status:**")
    print("""curl -X GET "http://localhost:8000/api/subscription/status" \\
     -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" """)

def main():
    """Main function"""
    hospitals = get_sample_hospitals()
    
    if hospitals:
        generate_test_commands(hospitals)
        
        print("\n🚀 Next Steps:")
        print("1. Start your application: cd hospital-chatbot/backend && uvicorn api.main:app --reload")
        print("2. Test API health: curl http://localhost:8000/health")
        print("3. View API docs: http://localhost:8000/docs")
        print("4. Use the API keys above to test chat functionality")
        print("5. Login with admin credentials to test dashboard features")

if __name__ == "__main__":
    main()
