# This files contains your custom actions which can be used to run
# custom Python code.
#
# See this guide on how to implement these action:
# https://rasa.com/docs/rasa/custom-actions


# This is a simple example for a custom action which utters "Hello World!"

# from typing import Any, Text, Dict, List
#
# from rasa_sdk import Action, Tracker
# from rasa_sdk.executor import CollectingDispatcher
#
#
# class ActionHelloWorld(Action):
#
#     def name(self) -> Text:
#         return "action_hello_world"
#
#     def run(self, dispatcher: CollectingDispatcher,
#             tracker: Tracker,
#             domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
#
#         dispatcher.utter_message(text="Hello World!")
#
#         return []




from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.forms import FormValidationAction
import httpx

class ActionRegisterPatient(Action):
    def name(self) -> Text:
        return "action_register_patient"
    
    async def run(
        self,
        dispatcher: CollectingDispatcher,
        tracker: Tracker,
        domain: Dict[Text, Any],
    ) -> List[Dict[Text, Any]]:
        
        # Extract patient information
        patient_data = {
            "name": tracker.get_slot("patient_name"),
            "email": tracker.get_slot("patient_email"),
            "phone": tracker.get_slot("patient_phone"),
            "hospital_id": self.extract_hospital_id(tracker.sender_id)
        }
        
        # Call backend API to register patient
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://backend:8000/api/patients/register",
                json=patient_data
            )
        
        if response.status_code == 200:
            dispatcher.utter_message(
                text="Great! You've been registered successfully. "
                     "Is there anything else I can help you with?"
            )
        else:
            dispatcher.utter_message(
                text="I'm sorry, there was an issue with registration. "
                     "Please try again or contact our support."
            )
        
        return []

class ActionBookAppointment(Action):
    def name(self) -> Text:
        return "action_book_appointment"
    
    async def run(self, dispatcher, tracker, domain):
        appointment_data = {
            "patient_name": tracker.get_slot("patient_name"),
            "patient_email": tracker.get_slot("patient_email"),
            "department": tracker.get_slot("department"),
            "preferred_date": tracker.get_slot("appointment_date"),
            "preferred_time": tracker.get_slot("appointment_time"),
            "hospital_id": self.extract_hospital_id(tracker.sender_id)
        }
        
        # Process appointment booking
        # Integration with hospital's scheduling system
        
        dispatcher.utter_message(
            text=f"Your appointment has been scheduled for {appointment_data['preferred_date']} "
                 f"at {appointment_data['preferred_time']} with {appointment_data['department']}. "
                 f"You'll receive a confirmation email shortly."
        )
        
        return []