# API Keys Setup Guide for Hospital Chatbot SaaS

This guide helps you obtain and configure all the API keys needed for your Hospital Chatbot SaaS platform.

## 🚀 **Required for Basic Functionality**

### 1. **Gemini AI API Key** (Required for AI features)
**Get from:** https://makersuite.google.com/app/apikey

**Steps:**
1. Go to Google AI Studio
2. Sign in with your Google account
3. Click "Get API Key"
4. Create a new API key
5. Copy the key and update your `.env` file:
   ```env
   GOOGLE_API_KEY=your-actual-gemini-api-key-here
   ```

**Cost:** Free tier available with generous limits

---

## 💳 **Required for SaaS Billing** (Optional for development)

### 2. **Stripe API Keys** (For subscription billing)
**Get from:** https://dashboard.stripe.com/apikeys

**Steps:**
1. Create a Stripe account
2. Go to Developers > API Keys
3. Copy the Publishable key and Secret key
4. For webhooks, go to Developers > Webhooks
5. Create endpoint: `http://your-domain.com/api/subscription/webhook`
6. Copy the webhook secret
7. Update your `.env` file:
   ```env
   STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_key
   STRIPE_SECRET_KEY=sk_test_your_actual_key
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_secret
   ```

**Cost:** 2.9% + 30¢ per transaction

---

## 🏥 **Optional HMS Integrations**

### 3. **Epic FHIR Integration** (Optional)
**Get from:** https://fhir.epic.com/Developer

**Steps:**
1. Register as Epic developer
2. Create a new app
3. Get client credentials
4. Update your `.env` file:
   ```env
   EPIC_CLIENT_ID=your-epic-client-id
   EPIC_CLIENT_SECRET=your-epic-client-secret
   ```

### 4. **Cerner FHIR Integration** (Optional)
**Get from:** https://fhir.cerner.com/smart/

**Steps:**
1. Register with Cerner
2. Create SMART on FHIR app
3. Get client credentials
4. Update your `.env` file:
   ```env
   CERNER_CLIENT_ID=your-cerner-client-id
   CERNER_CLIENT_SECRET=your-cerner-client-secret
   ```

---

## 📊 **Optional Monitoring & Analytics**

### 5. **Sentry Error Tracking** (Recommended for production)
**Get from:** https://sentry.io/

**Steps:**
1. Create Sentry account
2. Create new project
3. Copy the DSN
4. Update your `.env` file:
   ```env
   SENTRY_DSN=https://<EMAIL>/project-id
   ```

**Cost:** Free tier available

### 6. **Google Analytics** (Optional)
**Get from:** https://analytics.google.com/

**Steps:**
1. Create Google Analytics account
2. Set up property
3. Get tracking ID
4. Update your `.env` file:
   ```env
   GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
   ```

---

## ☁️ **Optional Cloud Storage**

### 7. **AWS S3** (For file storage)
**Get from:** https://aws.amazon.com/s3/

**Steps:**
1. Create AWS account
2. Create IAM user with S3 permissions
3. Create S3 bucket
4. Get access keys
5. Update your `.env` file:
   ```env
   AWS_ACCESS_KEY_ID=your-access-key
   AWS_SECRET_ACCESS_KEY=your-secret-key
   AWS_S3_BUCKET=your-bucket-name
   ```

---

## 🚀 **Quick Start (Minimum Required)**

For immediate testing, you only need:

1. **Gemini API Key** - Get from Google AI Studio
2. **Update .env file** with your Gemini key
3. **Start the application**

```bash
cd hospital-chatbot/backend
uvicorn api.main:app --reload
```

## 🧪 **Test Without External APIs**

You can test the basic functionality without any external API keys by:

1. Setting feature flags in `.env`:
   ```env
   ENABLE_GEMINI_AI=false
   ENABLE_STRIPE_BILLING=false
   ENABLE_EPIC_INTEGRATION=false
   ENABLE_CERNER_INTEGRATION=false
   ```

2. The system will use fallback responses for testing

## 📋 **Configuration Checklist**

- [ ] Gemini API Key configured
- [ ] Database connection working
- [ ] Application starts without errors
- [ ] Basic chat API responds
- [ ] Stripe keys (if using billing)
- [ ] Sentry DSN (if using error tracking)
- [ ] HMS integration keys (if needed)

## 🔒 **Security Notes**

1. **Never commit API keys to version control**
2. **Use environment variables for all secrets**
3. **Rotate keys regularly in production**
4. **Use test keys for development**
5. **Restrict API key permissions where possible**

## 🆘 **Troubleshooting**

### Common Issues:

1. **"Invalid API key" errors**
   - Check if key is correctly copied
   - Verify no extra spaces or quotes
   - Ensure key has proper permissions

2. **"Module not found" errors**
   - Run `pip install -r requirements.txt`
   - Check Python virtual environment

3. **Database connection errors**
   - Verify PostgreSQL is running
   - Check database credentials
   - Ensure database exists

## 📞 **Support**

If you need help with any API setup:
1. Check the official documentation for each service
2. Verify your account has proper permissions
3. Test with minimal examples first
4. Check application logs for detailed error messages
