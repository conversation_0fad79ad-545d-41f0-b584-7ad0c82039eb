# Sample Data Generator for Hospital Chatbot SaaS

This folder contains scripts to create PostgreSQL database from scratch and populate it with comprehensive dummy data for the Hospital Chatbot SaaS platform.

## 🚀 Quick Start (Complete Setup)

1. **Install dependencies**
   ```bash
   cd sample
   pip install -r requirements.txt
   ```

2. **Run complete setup (creates database + dummy data)**
   ```bash
   python setup_complete.py
   ```

This will:
- Create PostgreSQL database and user
- Create all tables
- Generate and insert dummy data
- Provide connection details

## 📁 Files Structure

```
sample/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── setup_complete.py        # Complete database setup + data
├── config.py               # Database configuration
├── database_creator.py     # Database and table creation
├── data_generator.py       # Dummy data generation
├── models.py              # Database models
└── scripts/
    ├── create_database.py  # Create database only
    ├── create_tables.py    # Create tables only
    ├── generate_dummy.py   # Generate dummy data only
    ├── insert_data.py      # Insert data only
    └── cleanup.py          # Clean up data
```

## 📊 Generated Data Overview

- **10 Hospitals** - Various types with different subscription plans
- **50 Users** - Admin and regular users across hospitals
- **500 Patients** - Diverse demographics with medical info
- **1000 Appointments** - Realistic scheduling across departments
- **2000 Chat Sessions** - Conversation flows with 10,000+ messages
- **Usage Metrics** - Monthly usage tracking
- **Subscription History** - Billing and plan changes

## 🔧 Manual Setup Steps

If you prefer step-by-step setup:

### Step 1: Create Database
```bash
python scripts/create_database.py
```

### Step 2: Create Tables
```bash
python scripts/create_tables.py
```

### Step 3: Generate Dummy Data
```bash
python scripts/generate_dummy.py
```

### Step 4: Insert Data
```bash
python scripts/insert_data.py
```

## 🏥 Sample Hospitals Created

1. **City General Hospital** (Premium Plan)
2. **Children's Medical Center** (Enterprise Plan)
3. **Heart & Vascular Institute** (Basic Plan)
4. **Metro Emergency Hospital** (Premium Plan)
5. **Women's Health Clinic** (Trial Plan)
6. **Orthopedic Specialty Center** (Basic Plan)
7. **Cancer Treatment Center** (Enterprise Plan)
8. **Mental Health Institute** (Premium Plan)
9. **Rehabilitation Hospital** (Basic Plan)
10. **Community Health Center** (Trial Plan)

## 👥 Sample Users

Each hospital has:
- 1 Admin user (<EMAIL> / admin123)
- 4 Regular users (<EMAIL> / user123)

## 🔐 Database Credentials

Default credentials created:
- **Database**: hospital_chatbot_saas
- **Username**: hospital_chatbot_user
- **Password**: HospitalBot2024!
- **Host**: localhost
- **Port**: 5432

## 🧪 Testing the Data

After setup, you can test with:

```bash
# Test database connection
python -c "from config import get_db_connection; print('✅ Database connected!' if get_db_connection() else '❌ Connection failed')"

# Check data counts
python scripts/verify_data.py
```

## 🗑️ Cleanup

To remove all test data:
```bash
python scripts/cleanup.py --confirm
```

To drop the entire database:
```bash
python scripts/cleanup.py --drop-database --confirm
```
