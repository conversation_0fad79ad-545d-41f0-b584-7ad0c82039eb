# Sample Data Generator for Hospital Chatbot SaaS

This folder contains scripts to generate comprehensive dummy data for the Hospital Chatbot SaaS platform. The data includes hospitals, users, patients, appointments, chat sessions, and subscription information.

## Files Structure

```
sample/
├── README.md                 # This file
├── requirements.txt          # Python dependencies for data generation
├── config.py                # Database configuration
├── data_generator.py        # Main data generation script
├── models.py               # Database models (copied from main project)
├── dummy_data/             # Generated data files
│   ├── hospitals.json
│   ├── users.json
│   ├── patients.json
│   ├── appointments.json
│   └── chat_sessions.json
└── scripts/
    ├── generate_data.py    # Generate dummy data files
    ├── insert_data.py      # Insert data into database
    └── cleanup_data.py     # Clean up test data

```

## Quick Start

1. **Install dependencies**
   ```bash
   cd sample
   pip install -r requirements.txt
   ```

2. **Configure database connection**
   ```bash
   cp config.py.example config.py
   # Edit config.py with your PostgreSQL credentials
   ```

3. **Generate dummy data**
   ```bash
   python scripts/generate_data.py
   ```

4. **Insert data into database**
   ```bash
   python scripts/insert_data.py
   ```

## Generated Data

### Hospitals (10 hospitals)
- Various hospital types (General, Specialty, Children's, etc.)
- Different subscription plans (trial, basic, premium, enterprise)
- Realistic contact information and settings
- Custom branding configurations

### Users (50 users)
- Admin and regular users for each hospital
- Realistic names and email addresses
- Proper role assignments

### Patients (500 patients)
- Diverse patient demographics
- Medical record numbers
- Insurance information
- Emergency contacts

### Appointments (1000 appointments)
- Various departments and specialties
- Different appointment statuses
- Realistic date/time scheduling
- Patient contact information

### Chat Sessions (2000 sessions)
- Realistic conversation flows
- Various intents and responses
- Message timestamps and metadata
- Visitor tracking

### Usage Metrics
- Daily usage statistics
- Message counts per hospital
- Subscription usage tracking

## Configuration

Edit `config.py` to match your database settings:

```python
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "hospital_chatbot_saas",
    "username": "hospital_chatbot_user",
    "password": "HospitalBot2024!"
}
```

## Usage Examples

### Generate specific amount of data
```bash
python scripts/generate_data.py --hospitals 5 --patients 100 --appointments 200
```

### Insert only specific data types
```bash
python scripts/insert_data.py --only hospitals,users
```

### Clean up test data
```bash
python scripts/cleanup_data.py --confirm
```

## Data Quality

- **Realistic Names**: Using Faker library for authentic names
- **Valid Emails**: Proper email format with hospital domains
- **Consistent Relationships**: Proper foreign key relationships
- **Temporal Logic**: Realistic timestamps and date ranges
- **Medical Accuracy**: Appropriate medical terminology and departments

## Safety Features

- **Backup Creation**: Automatic database backup before insertion
- **Rollback Support**: Ability to undo data insertion
- **Validation**: Data validation before database insertion
- **Conflict Resolution**: Handles duplicate data gracefully
