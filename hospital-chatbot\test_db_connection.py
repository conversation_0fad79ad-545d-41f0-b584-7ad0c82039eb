#!/usr/bin/env python3
"""
Test Database Connection Script
Verifies if the DATABASE_URL in .env is correct
"""

import os
import sys
from dotenv import load_dotenv

def test_database_connection():
    """Test database connection using the DATABASE_URL from .env"""
    print("🔍 Testing Database Connection...")
    print("=" * 50)
    
    # Load environment variables
    env_path = os.path.join(os.path.dirname(__file__), 'backend', '.env')
    load_dotenv(env_path)
    
    database_url = os.getenv('DATABASE_URL')
    print(f"📋 Database URL: {database_url}")
    
    if not database_url:
        print("❌ DATABASE_URL not found in .env file")
        return False
    
    # Parse the URL
    if database_url.startswith('postgresql://'):
        parts = database_url.replace('postgresql://', '').split('@')
        if len(parts) == 2:
            user_pass = parts[0]
            host_db = parts[1]
            
            if ':' in user_pass:
                username, password = user_pass.split(':', 1)
                print(f"👤 Username: {username}")
                print(f"🔑 Password: {'*' * len(password)}")
            
            if '/' in host_db:
                host_port, database = host_db.split('/', 1)
                if ':' in host_port:
                    host, port = host_port.split(':')
                    print(f"🏠 Host: {host}")
                    print(f"🔌 Port: {port}")
                else:
                    host = host_port
                    port = "5432"
                    print(f"🏠 Host: {host}")
                    print(f"🔌 Port: {port} (default)")
                
                print(f"🗄️  Database: {database}")
    
    # Test connection with psycopg2
    print("\n🧪 Testing psycopg2 connection...")
    try:
        import psycopg2
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ psycopg2 connection successful!")
        print(f"📊 PostgreSQL version: {version}")
        cursor.close()
        conn.close()
    except ImportError:
        print("⚠️  psycopg2 not installed, skipping direct test")
    except Exception as e:
        print(f"❌ psycopg2 connection failed: {e}")
        return False
    
    # Test connection with SQLAlchemy
    print("\n🧪 Testing SQLAlchemy connection...")
    try:
        from sqlalchemy import create_engine, text
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT current_database(), current_user;"))
            db_name, user = result.fetchone()
            print(f"✅ SQLAlchemy connection successful!")
            print(f"🗄️  Connected to database: {db_name}")
            print(f"👤 Connected as user: {user}")
            
            # Check if tables exist
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """))
            tables = [row[0] for row in result.fetchall()]
            
            if tables:
                print(f"📋 Found {len(tables)} tables:")
                for table in tables:
                    print(f"   - {table}")
            else:
                print("⚠️  No tables found - database may not be initialized")
                
    except Exception as e:
        print(f"❌ SQLAlchemy connection failed: {e}")
        return False
    
    return True

def check_sample_data():
    """Check if sample data exists"""
    print("\n🏥 Checking Sample Data...")
    
    try:
        from sqlalchemy import create_engine, text
        database_url = os.getenv('DATABASE_URL')
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Check hospitals
            result = conn.execute(text("SELECT COUNT(*) FROM hospitals;"))
            hospital_count = result.fetchone()[0]
            print(f"🏥 Hospitals: {hospital_count}")
            
            # Check users
            result = conn.execute(text("SELECT COUNT(*) FROM users;"))
            user_count = result.fetchone()[0]
            print(f"👥 Users: {user_count}")
            
            # Check patients
            result = conn.execute(text("SELECT COUNT(*) FROM patients;"))
            patient_count = result.fetchone()[0]
            print(f"🏥 Patients: {patient_count}")
            
            # Check chat sessions
            result = conn.execute(text("SELECT COUNT(*) FROM chat_sessions;"))
            session_count = result.fetchone()[0]
            print(f"💬 Chat Sessions: {session_count}")
            
            if hospital_count > 0:
                print("✅ Sample data found!")
                return True
            else:
                print("⚠️  No sample data found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking sample data: {e}")
        return False

def main():
    """Main test function"""
    print("🏥 Hospital Chatbot SaaS - Database Connection Test")
    print("=" * 60)
    
    # Test database connection
    connection_ok = test_database_connection()
    
    if connection_ok:
        # Check sample data
        data_ok = check_sample_data()
        
        print("\n" + "=" * 60)
        print("📋 SUMMARY")
        print("=" * 60)
        
        if connection_ok and data_ok:
            print("🎉 Database connection and sample data are working perfectly!")
            print("\n✅ Your DATABASE_URL is correct!")
            print("✅ Sample data is available for testing!")
            print("\n🚀 You can now start your application:")
            print("   cd backend")
            print("   uvicorn api.main:app --reload")
        elif connection_ok:
            print("✅ Database connection works!")
            print("⚠️  But no sample data found.")
            print("\n💡 To add sample data:")
            print("   cd sample")
            print("   python setup_complete.py")
        else:
            print("❌ Database connection failed!")
    else:
        print("\n❌ Database connection test failed!")
        print("\n💡 Troubleshooting steps:")
        print("1. Check if PostgreSQL is running")
        print("2. Verify database exists: hospital_chatbot_saas")
        print("3. Verify user exists: hospital_chatbot_user")
        print("4. Check password: HospitalBot2024!")
        print("5. Run sample setup: cd sample && python setup_complete.py")

if __name__ == "__main__":
    main()
